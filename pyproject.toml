[project]
name = "whale-ai"
version = "0.1.0"
description = ""
authors = [
    {name = "gao<PERSON><PERSON><EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11,<4.0"
dependencies = [
    "streamlit (>=1.48.0,<2.0.0)",
    "agno (>=1.7.9,<2.0.0)",
    "httpx (>=0.28.1,<0.29.0)",
    "numpy (>=2.3.2,<3.0.0)",
    "pandas (>=2.3.1,<3.0.0)",
    "plotly (>=6.2.0,<7.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
