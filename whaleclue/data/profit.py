#!/usr/bin/env python
# -*- coding: utf-8 -*-
# !/usr/bin/env python
# -*- coding: utf-8 -*-
# https://profit.com/, 获取investor的头像
from datetime import datetime
from typing import List, Optional, Literal, Dict, Any

import httpx

HEADERS = {
    "Connection": "keep-alive",
    "Expires": "-1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36"
    )
}


def get_timestamp(time: str | int | datetime):
    if isinstance(time, datetime):
        return int(time.timestamp())
    elif isinstance(time, str):
        return int(datetime.strptime(time, '%Y-%m-%d').timestamp())
    elif isinstance(time, int):
        return time
    else:
        raise ValueError(f'Invalid time type: {type(time)}')


def get_time_range(start_time: Optional[str | int | datetime] = None,
                   end_time: Optional[str | int | datetime] = None,
                   use_timestamp: bool = False):
    time_range_ts = ''
    if start_time is not None:
        if isinstance(start_time, datetime):
            start_time = int(start_time.timestamp())
        if use_timestamp:
            time_range_ts += str(get_timestamp(start_time))
        else:
            time_range_ts += str(start_time)
    if end_time is not None:
        if isinstance(end_time, datetime):
            end_time = int(end_time.timestamp())
        if use_timestamp:
            time_range_ts += ':' + str(get_timestamp(end_time))
        else:
            time_range_ts += ':' + str(end_time)
    else:
        time_range_ts += ':'

    return time_range_ts


class ProfitDataClient:
    def __init__(self, http_client: httpx.AsyncClient = None):
        self._http_client = http_client or httpx.AsyncClient()

    def _fetch_data(self, url: str, params: Dict[str, Any] = None, data_key: str = 'data',
                    fetch_all: bool = False):
        # 分页获取数据, 默认分页字段为 skip 和 limit
        if fetch_all and ('skip' in params or 'offset' in params) and 'limit' in params:
            items = []
            while True:
                response = httpx.get(url, params=params, headers=HEADERS, timeout=30)
                response.raise_for_status()
                if response.status_code == 200:
                    data = response.json()
                    if data is not None and data_key is not None and data_key in data:
                        data = data[data_key]
                    if len(data) == 0:
                        break
                    items.extend(data)
                    if 'skip' in params:
                        params['skip'] = int(params['skip']) + int(params['limit'])
                    elif 'offset' in params:
                        params['offset'] = int(params['offset']) + int(params['limit'])
                else:
                    break
            return items
        else:
            response = httpx.get(url, params=params, headers=HEADERS, timeout=30)
            response.raise_for_status()
            if response.status_code == 200:
                data = response.json()
                if data is not None and data_key is not None and data_key in data:
                    data = data[data_key]
                return data
        return None

    def get_slugs_to_ciks(self):
        url = 'https://api2.profit.com/terminal/investors/slugs-to-ciks'
        return self._fetch_data(url)

    def get_investors_reporting_dates(self, investor_type: str, slug: str = None, cik: str = None):
        # investor_type: hedge_fund_manager, corporate_insider
        url = f'https://api2.profit.com/terminal/investors/reporting-dates'
        params = {'investor_type': investor_type}
        if slug is not None:
            params['slug'] = slug
        if cik is not None:
            params['cik'] = cik
        return self._fetch_data(url, params=params)

    def get_investor_performance(self, cik: str = None, slug: str = None,
                                 start_date: Optional[str | int | datetime] = None,
                                 end_date: Optional[str | int | datetime] = None,
                                 time_range: str = None, time_frame: str = '1w'):
        # time_range: time_range=2024-03-01:
        # time_frame: 1d, 1w, 1M
        if cik is None and slug is None:
            raise ValueError('At least one of cik or slug should be provided')
        if time_range is None:
            time_range = get_time_range(start_date, end_date)
        url = 'https://api2.profit.com/terminal/investors/performance'
        params = {}
        if cik is not None:
            params['cik'] = cik
        if slug is not None:
            params['slug'] = slug
        if time_range is not None:
            params['time_range'] = time_range
        if time_frame is not None:
            params['timeframe'] = time_frame
        return self._fetch_data(url, params=params)

    def get_latest_trades(self, investor_type: str, limit: int = 10):
        url = f'https://api2.profit.com/terminal/investors/trades/latest'
        params = {
            'investor_type': investor_type,
            'lang': 'en',
            'trade_count': limit
        }
        return self._fetch_data(url, params=params)

    def get_investor_holdings(self, investor_type: str, holdings_type: str, report_date: str,
                              skip: Optional[int] = None, limit: Optional[int] = 100):
        # holdings_type: owned, buy, sell, new-buys, sector
        url = f'https://api2.profit.com/terminal/investors/holdings/{holdings_type}'
        params = {
            'investor_type': investor_type,
            'report_date': report_date
        }
        if skip is not None:
            params['skip'] = str(skip)
        if limit is not None:
            params['limit'] = str(limit)
        return self._fetch_data(url, params=params)

    def get_investor_summary(self, investor_type: str, lang: str = 'en'):
        """
        :param investor_type: investor_type, e.g., hedge_fund_manager, corporate_insider
        :param lang: en
        """
        url = f'https://api2.profit.com/terminal/investors/summary'
        params = {
            'investor_type': investor_type,
            'lang': lang or 'en'
        }
        return self._fetch_data(url, params=params)

    def get_investor_stories(self, investor_type: str, limit: int = 3):
        url = f'https://api2.profit.com/terminal/investors/stories'
        params = {
            'investor_type': investor_type,
            'trade_count': limit
        }
        return self._fetch_data(url, params=params)

    def get_investor_portfolio(self, slug: str, report_date: str = None, lang: str = 'en'):
        url = f'https://api2.profit.com/terminal/investors/portfolio'
        params = {
            'slug': slug,
            'lang': lang
        }
        if report_date is not None:
            params['report_date'] = report_date
        return self._fetch_data(url, params=params)

    def get_investor_ownership(self, cik: str, ticker: str):
        url = f'https://api2.profit.com/terminal/investors/ownership?cik={cik}&ticker={ticker}'
        return self._fetch_data(url)

    def get_investor_trades(self, slug: Optional[str] = None, cik: Optional[str] = None,
                            ticker: Optional[str] = None,
                            trade_type: str = 'all',
                            form_type: Optional[str] = None,
                            time_range: str = None,
                            start_date: Optional[str | int | datetime] = None,
                            end_date: Optional[str | int | datetime] = None,
                            skip: int = 0, limit: int = 100,
                            fetch_all: bool = False):
        # slug or cik + ticker
        # trade_type: all, buy, sell
        # time_range: time_range=2024-10-01:2025-01-01
        url = f'https://api2.profit.com/terminal/investors/trades'
        params = {
            'trade_type': trade_type,
            'skip': skip,
            'limit': limit
        }
        if slug is not None:
            params['slug'] = slug
        if cik is not None:
            params['cik'] = cik
        if ticker is not None:
            params['ticker'] = ticker
        if form_type is not None:
            params['form_type'] = form_type
        if time_range is not None:
            params['time_range'] = time_range
        else:
            time_range = get_time_range(start_date, end_date)
            if time_range != '':
                params['time_range'] = time_range
        return self._fetch_data(url, params=params, fetch_all=fetch_all)

    def get_investor_sector_allocation_history(self, slug: str):
        url = f'https://api2.profit.com/terminal/investors/sector-allocation-history'
        params = {
            'slug': slug
        }
        return self._fetch_data(url, params=params)

    def get_investor_form_types(self, slug: Optional[str] = None, cik: Optional[str] = None):
        if slug is None and cik is None:
            return None
        url = f'https://api2.profit.com/terminal/investors/filings/form-types'
        params = {}
        if slug is not None:
            params['slug'] = slug
        if cik is not None:
            params['cik'] = cik
        return self._fetch_data(url, params=params)

    def get_investor_filings(self, slug: Optional[str] = None, cik: Optional[str] = None,
                             form_type: Optional[str] = None, start_date: Optional[str] = None,
                             skip: Optional[int] = 0, limit: Optional[int] = 100, lang: str = None,
                             fetch_all: bool = False):
        if slug is None and cik is None:
            return None
        url = f'https://api2.profit.com/terminal/investors/filings'
        params = {}
        if slug is not None:
            params['slug'] = slug
        if cik is not None:
            params['cik'] = cik
        if form_type is not None:
            params['form_type'] = form_type
        if skip is not None:
            params['skip'] = str(skip)
        if limit is not None:
            params['limit'] = str(limit)
        if lang is not None:
            params['lang'] = lang

        if not fetch_all:
            response = httpx.get(url, params=params, headers=HEADERS, timeout=30)
            response.raise_for_status()
            if response.status_code == 200:
                data = response.json()
                if data is not None and 'data' in data:
                    return data['total'], data['data']
            return None, None

        items = []
        while True:
            response = httpx.get(url, params=params, headers=HEADERS, timeout=30)
            response.raise_for_status()
            if response.status_code == 200:
                data = response.json()
                if data is not None and 'data' in data:
                    data = data['data']
                if len(data) == 0 or (
                    start_date is not None and any(data['filing_date'] < start_date for data in data)):
                    if len(data) > 0 and start_date is not None:
                        remaining_items = [item for item in data if item['filing_date'] >= start_date]
                        items.extend(remaining_items)
                    break
                items.extend(data)
                if 'skip' in params:
                    params['skip'] = str(int(params['skip']) + int(params['limit']))
            else:
                break
        return len(items), items

    def search_instruments(self, query: Optional[str] = None, tickers: List[str] | str = None,
                           skip: Optional[int] = 0,
                           limit: Optional[int] = None):
        """
        :param query: Search query
        :param tickers: List of tickers
        :param skip: Number of results to skip, skip + limit <= 10000
        :param limit: Number of results to return, limit <= 100
        """
        # https://api2.profit.com/terminal/search/instruments/filters?query=AA
        url = 'https://api2.profit.com/terminal/search/instruments'

        params = {}

        if query is not None:
            params['query'] = query

        if tickers is not None:
            if isinstance(tickers, str):
                # If a single ticker is provided as string
                params = {'T': tickers}
            else:
                # If multiple tickers are provided as a list
                # The requests library allows using a list for duplicate parameter names
                params = {'T': tickers}

        if skip is not None:
            params['skip'] = str(skip)

        if limit is not None:
            params['limit'] = str(limit)

        return self._fetch_data(url, params=params)

    def search_investors(self, query: str, investor_type: Optional[str | List[str]] = None,
                         skip: Optional[int] = 0,
                         limit: Optional[int] = None,
                         lang: str = 'en'):
        """
        :param query: Search query
        :param investor_type: Investor type, e.g., hedge_fund_manager, corporate_insider
        :param skip: Number of results to skip
        :param limit: Number of results to return
        :param lang: Language to return results in
        """
        url = 'https://api2.profit.com/terminal/search/investors'

        params = {
            'search': query or '',
            'lang': lang or 'en'
        }
        if investor_type is not None:
            params['investor_type'] = investor_type
        else:
            params['investor_type'] = ['hedge_fund_manager', 'corporate_insider']

        if skip is not None:
            params['skip'] = str(skip)
        if limit is not None:
            params['limit'] = str(limit)

        return self._fetch_data(url, params=params)

    def search_events(self, query: str, asset_class: Optional[str] = None,
                      start_time: Optional[str | datetime | int] = None,
                      end_time: Optional[str | datetime | int] = None, skip: Optional[int] = 0,
                      limit: Optional[int] = None):
        """
        :param query: Search query
        :param asset_class: Asset class, e.g., STOCKS, CRYPTO, FOREX, COMMODITIES
        :param start_time: Start time
        :param end_time: End time
        :param skip: Number of results to skip
        :param limit: Number of results to return
        """
        url = 'https://api2.profit.com/terminal/search/events?asset_class=STOCKS&limit=20&skip=40&time_range_ts=1745921095%3A'
        params = {

        }
        if query is not None:
            params['search'] = query
        time_range_ts = get_time_range(start_time, end_time)
        params['time_range_ts'] = time_range_ts
        if asset_class is not None:
            params['asset_class'] = asset_class
        if skip is not None:
            params['skip'] = str(skip)
        if limit is not None:
            params['limit'] = str(limit)
        return self._fetch_data(url, params=params)

    def get_available_data(self, tickers: List[str] | str):
        url = 'https://api2.profit.com/terminal/available-data'
        params = {'t': tickers or []}
        return self._fetch_data(url, params=params)

    def get_quote(self, tickers: List[str] | str):
        url = 'https://api2.profit.com/terminal/quote'
        params = {'t': tickers or []}

        return self._fetch_data(url, params=params)

    def get_prices(self, ticker: str, start_time: Optional[str | int | datetime] = None,
                   end_time: Optional[str | int | datetime] = None,
                   time_frame: str = '1d'):
        url = f'https://api2.profit.com/terminal/time-series/prices/{ticker}'
        params = {}
        if start_time is not None:
            params['start_time'] = get_timestamp(start_time)
        if end_time is not None:
            params['end_time'] = get_timestamp(end_time)
        # time_range=1730217600:
        if time_frame is not None:
            params['time_frame'] = time_frame
        return self._fetch_data(url, params=params)

    def get_previous_closes(self, ticker: str, deprecated: bool = False, periods: List[str] | str = '1d',
                            time_frame: str = '1d'):
        """
        :param ticker: Ticker
        :param deprecated: Deprecated
        :param periods: Periods, e.g., 1d, 1w, 1M, 6M, 12M, 36M
        :param time_frame: Time frame, e.g., 1d
        """
        url = f'https://api2.profit.com/terminal/time-series/previous-close/{ticker}'
        params = {}
        if deprecated is not None:
            params['deprecated'] = deprecated
        if periods is not None:
            params['p'] = periods
        if time_frame is not None:
            params['time_frame'] = time_frame
        return self._fetch_data(url, params=params)

    def get_intersections(self, tickers: List[str] | str, time_frame: str = '1h',
                          start_time: Optional[str | int | datetime] = None,
                          end_time: Optional[str | int | datetime] = None):
        url = f'https://api2.profit.com/terminal/time-series/intersections/{tickers}'
        params = {
            'timeframe': time_frame
        }
        date_range = get_time_range(start_time, end_time)
        if date_range != '':
            params['time_range'] = date_range
        return self._fetch_data(url, params=params)

    def get_market_hours(self, ticker: str):
        url = f'https://api2.profit.com/terminal/time-series/market-hours?t={ticker}'
        return self._fetch_data(url)

    def get_roi(self, tickers: List[str] | str, category: Literal['period', 'cumulative'] = 'period',
                time_frame: str = '1m',
                start_time: Optional[str | int | datetime] = None,
                end_time: Optional[str | int | datetime] = None):
        """
        :param category: Category, e.g., period, cumulative
        :param time_frame: Time frame, e.g., 1m, 5m, 15m, 30m, 1h, 4h, 1d
        :param start_time: Start time
        :param end_time: End time
        """
        # time_range=1742241600:1742296340
        url = f'https://api2.profit.com/terminal/time-series/roi/{category}'
        time_range = get_time_range(start_time, end_time)
        params = {
            'T': tickers,
            'time_range': time_range,
            'timeframe': time_frame
        }
        return self._fetch_data(url, params=params)

    def get_time_series(self, ticker: str, time_frame: str = '1d',
                        start_time: Optional[str | int | datetime] = None,
                        end_time: Optional[str | int | datetime] = None):
        # time_series_type: roi, price, volume, mcap, pe, ps, pb, div, beta, vwap, vwap_period, vwap_period_period
        url = f'https://api2.profit.com/terminal/time-series/candles/{ticker}'
        params = {
            'time_frame': time_frame
        }
        time_range = get_time_range(start_time, end_time, use_timestamp=True)
        if time_range != '':
            params['time_range'] = time_range
        return self._fetch_data(url, params=params)

    def get_time_series_ranges(self, tickers: List[str] | str, periods: List[str] | str = '1d'):
        """
        :params tickers
        :params periods, 1d,1w,1M,6M,12M,36M
        """
        url = 'https://api2.profit.com/terminal/time-series/ranges'
        params = {
            'T': tickers,
            'p': periods
        }
        return self._fetch_data(url, params=params)

    def get_calendar_filters(self, asset_class: str, field: str,
                             start_time: Optional[str | int | datetime] = None,
                             end_time: Optional[str | int | datetime] = None):
        """
        :param asset_class: Asset class, e.g., crypto, forex
        :param field: Field, e.g., forex(impact, country_iso, currency), crypto(important,tag,symbol),
        stock(sector, country_iso), commodities(impact, country_iso)
        :param start_time: Start time
        :param end_time: End time
        """
        url = f'https://api2.profit.com/terminal/calendar/{asset_class}/{field}'
        params = {}
        if start_time is not None:
            params['start_time'] = get_timestamp(start_time)
        if end_time is not None:
            params['end_time'] = get_timestamp(end_time)
        return self._fetch_data(url, params=params)

    def get_calendar(self, asset_class: str, event_type: Optional[str] = None,
                     start_time: Optional[str | int | datetime] = None,
                     end_time: Optional[str | int | datetime] = None,
                     skip: Optional[int] = 0,
                     limit: Optional[int] = 100, fetch_all: bool = False, **kwargs):
        """
        :param asset_class: Asset class, e.g., crypto, forex, stock, commodities
        :param event_type: Category, e.g., only for stock(earnings, dividends, ipo, splits)
        :param start_time: Start time
        :param end_time: End time
        :param skip: Skip
        :param limit: Limit
        :param fetch_all
        """
        # https://api2.profit.com/terminal/calendar/forex?take=100&query=&start_time=1745856000&end_time=1746547199
        # crypto: source_reliable=true/false
        url = f'https://api2.profit.com/terminal/calendar/{asset_class}'
        if event_type is not None:
            url += f'/{event_type}'
        params = {}
        if start_time is not None:
            params['start_time'] = get_timestamp(start_time)
        if end_time is not None:
            params['end_time'] = get_timestamp(end_time)
        # start_date=2025-04-29&end_date=2025-05-05
        if kwargs is not None:
            for k, v in kwargs.items():
                params[k] = v
        if skip is not None:
            params['skip'] = skip
        if limit is not None:
            params['limit'] = limit
        return self._fetch_data(url, params=params, data_key='events', fetch_all=fetch_all)

    def get_stock_holders(self, ticker: str):
        # AAPL.US
        url = f'https://api2.profit.com/terminal/investors/stock-holders?ticker={ticker}'
        return self._fetch_data(url)

    def get_transactions(self, ticker: str, transaction_codes: Optional[str] = None, skip: Optional[int] = 0,
                         limit: Optional[int] = 100, fetch_all: bool = False):
        # https://api2.profit.com/terminal/transactions/top
        # https://api2.profit.com/terminal/transactions/top?trades_count=6
        url = f'https://api2.profit.com/terminal/transactions'
        params = dict()
        params['ticker'] = ticker
        if transaction_codes is not None:
            params['transaction_codes'] = transaction_codes
        if skip is not None:
            params['skip'] = skip
        if limit is not None:
            params['limit'] = limit
        return self._fetch_data(url, params=params, data_key='data', fetch_all=fetch_all)

    def get_media_filters(self, media_type: str, asset_class: str = None):
        """
        :param media_type: Media type, e.g., news, tweets
        :param asset_class: Asset class, e.g., STOCKS, CRYPTO, FOREX, COMMODITIES
        """
        url = f'https://api2.profit.com/terminal/search/media/filters'
        params = {
            'media_type': media_type
        }
        if asset_class is not None:
            params['asset_class'] = asset_class
        return self._fetch_data(url, params=params)

    def search_media(self, query: Optional[str] = None,
                     media_type: Optional[str] = None,
                     ticker: Optional[str | List[str]] = None,
                     asset_class: Optional[str] = None,
                     tagged_only: Optional[bool] = None,
                     with_image_only: Optional[bool] = None,
                     skip: Optional[int] = 0,
                     limit: Optional[int] = None):
        """
        :param query: Search query
        :param media_type: Media type, e.g., news, tweets
        :param ticker: Ticker
        :param asset_class: Asset class, e.g., STOCKS, CRYPTO, FOREX, COMMODITIES
        :param tagged_only: Tagged only
        :param with_image_only: With image only
        :param skip: Number of results to skip
        :param limit: Number of results to return
        """
        # https://api2.profit.com/terminal/feed/
        # https://api2.profit.com/terminal/search/media?limit=20&skip=0&source=2281314234&source=2207129125&source=1333467482&source=220283722&source=AMB%20Crypto&source=Cointelegraph&source=CoinDesk
        url = 'https://api2.profit.com/terminal/search/media'
        params = {}
        if query is not None:
            params['query'] = query
        if asset_class is not None:
            params['asset_class'] = asset_class
        if ticker is not None:
            params['ticker'] = ticker
        if tagged_only is not None:
            params['tagged_only'] = tagged_only
        if with_image_only is not None:
            params['with_image_only'] = with_image_only
        if skip is not None:
            params['skip'] = skip
        if limit is not None:
            params['limit'] = limit
        return self._fetch_data(url, params=params)

    def get_publications(self, publication_type: Optional[str] = None,
                         start_time: Optional[str | int | datetime] = None,
                         end_time: Optional[str | int | datetime] = None,
                         published: Optional[bool] = None,
                         with_content: Optional[bool] = None,
                         skip: Optional[int] = 0,
                         limit: Optional[int] = None,
                         lang: str = 'en'):
        """
        :param publication_type: Publication type, e.g., blog, market_pulse, video, release, weekly_brief
        :param start_time: Start time
        :param end_time: End time
        :param published: Published
        :param with_content: With content
        :param skip: Number of results to skip
        :param limit: Number of results to return
        :param lang: Language
        """
        # title=
        url = 'https://api2.profit.com/terminal/publications'
        params = dict()
        params['lang'] = lang or 'en'
        publication_time = get_time_range(start_time, end_time)
        if publication_time is not None and publication_time != '':
            params['publication_time'] = publication_time
        if publication_type is not None:
            params['type'] = publication_type
        if published is not None:
            params['published'] = str(published)
        if with_content is not None:
            params['with_content'] = str(with_content)
        if skip is not None:
            params['skip'] = skip
        if limit is not None:
            params['limit'] = limit
        return self._fetch_data(url, params=params)

    def get_sector_performance(self):
        url = 'https://api2.profit.com/terminal/overview/sector_performance'
        return self._fetch_data(url)

    def get_overview(self, asset_class: str = None,
                     events_limit: Optional[int] = None,
                     news_limit: Optional[int] = None):
        """
        :param asset_class: Asset class, e.g., stocks, etf, crypto, commodities
        :param events_limit: Number of events to return
        :param news_limit: Number of news items to return
        """
        if asset_class is None:
            url = 'https://api2.profit.com/terminal/overview/'
        else:
            url = f'https://api2.profit.com/terminal/overview/{asset_class}'
        params = {}

        if events_limit is not None:
            params['events_limit'] = events_limit
        if news_limit is not None:
            params['news_limit'] = news_limit
        return self._fetch_data(url, params=params)

    def get_langs(self, category: str, lang: str = 'en'):
        """
        :param category: category, e.g., asset-class, commom, consent-mode, coutries, currencies,
            period, sector, timezone, translation, widget-instance
        :param lang: language, e.g., en, zh
        """
        url = f'https://profit.com/terminal/langs/{lang}/{category}.json'
        return self._fetch_data(url)

    def get_stock_filings(self, ticker: str, offset: Optional[int] = 0, limit: Optional[int] = 100,
                          start_date: Optional[int | str] = None, fetch_all: bool = False):
        url = f'https://api2.profit.com/terminal/iw/stocks/filings/{ticker}'
        params = {}
        if offset is not None:
            params['offset'] = offset
        if limit is not None:
            params['limit'] = limit

        if fetch_all and limit is not None and offset is not None:
            if start_date is not None:
                start_ts = get_timestamp(start_date)
            else:
                start_ts = None
            items = []
            while True:
                response = self._http_client.get(url, params=params, headers=HEADERS, timeout=30)
                response.raise_for_status()
                if response.status_code == 200:
                    data = response.json()
                    if data is not None and 'filings' in data:
                        data = data['filings']
                    if len(data) == 0 or (start_ts is not None and any(data['date_filed'] < start_ts for data in data)):
                        if len(data) > 0 and start_ts is not None:
                            remaining_items = [item for item in data if item['date_filed'] >= start_ts]
                            items.extend(remaining_items)
                        break
                    items.extend(data)
                    if 'offset' in params:
                        params['offset'] = int(params['offset']) + int(params['limit'])
                else:
                    break
            return items
        else:
            return self._fetch_data(url, params=params, data_key='filings', fetch_all=fetch_all)

    def get_stock_info_widget(self, ticker: str, category: str, lang: str = 'en'):
        """
        :param ticker: Ticker
        :param category: category, e.g., header, general, events, profile, statistics, earnings, insider_trades, financials, trends, filings, holders
        :param lang: language, e.g., en, zh
        """
        # https://api2.profit.com/terminal/iw/stocks/filings/AAPL.US?limit=30&offset=0
        # https://api2.profit.com/terminal/search/media?limit=20&skip=0&ticker=AAPL.US
        # https://messenger.profit.com/api/chats?include_event_message=true

        url = f'https://api2.profit.com/terminal/iw/stocks/{category}/{ticker}?lang={lang}'
        return self._fetch_data(url)

    def get_etf_info_widget(self, ticker: str, category: str, lang: str = 'en'):
        """
        :param ticker: Ticker
        :param category: category, e.g., header, general, fundamentals, holdings, daily-returns, risks
        :param lang: Language, e.g., en, zh
        """
        url = f'https://api2.profit.com/terminal/iw/etf/{category}/{ticker}?lang={lang}'
        return self._fetch_data(url)

    def get_fund_info_widget(self, ticker: str, category: str, lang: str = 'en'):
        """
        :param ticker: Ticker
        :param category: category, e.g., header, general, fundamentals, holdings, daily-returns, risks
        :param lang: Language, e.g., en, zh
        """
        url = f'https://api2.profit.com/terminal/iw/funds/{category}/{ticker}?lang={lang}'
        return self._fetch_data(url)

    def get_crypto_info_widget(self, ticker: str, category: str, lang: str = 'en'):
        """
        :param ticker: Ticker
        :param category: category, e.g., header, general, volume/history, analysis
        :param lang: Language, e.g., en, zh
        """
        url = f'https://api2.profit.com/terminal/iw/crypto/{category}/{ticker}?lang={lang}'
        return self._fetch_data(url)

    def get_crypto_volume_history(self, ticker: str, currency: str = 'USD', time_frame: str = '1h',
                                  limit: int = 24,
                                  lang: str = 'en'):
        url = f'https://api2.profit.com/terminal/iw/crypto/volume/history/{ticker}'
        params = {
            'currency': currency,
            'time_frame': time_frame,
            'limit': limit,
            'lang': lang
        }
        return self._fetch_data(url, params=params)

    def get_forex_info_widget(self, ticker: str, category: str, lang: str = 'en'):
        """
        :param ticker: Ticker
        :param category: category, e.g., header, general
        :param lang: Language, e.g., en, zh
        """
        url = f'https://api2.profit.com/terminal/iw/forex/{category}/{ticker}?lang={lang}'
        return self._fetch_data(url)

    def get_commodities_info_widget(self, ticker: str, category: str, lang: str = 'en'):
        """
        :param ticker: Ticker
        :param category: category, e.g., header, general
        :param lang: Language, e.g., en, zh
        """
        url = f'https://api2.profit.com/terminal/iw/commodities/{category}/{ticker}?lang={lang}'
        return self._fetch_data(url)

    def get_indices_info_widget(self, ticker: str, category: str, lang: str = 'en'):
        """
        :param ticker: Ticker
        :param category: category, e.g., header, general, components
        :param lang: Language, e.g., en, zh
        """
        url = f'https://api2.profit.com/terminal/iw/indexes/{category}/{ticker}?lang={lang}'
        return self._fetch_data(url)

    def get_info_widget(self, asset_class: str, ticker: str, category: str, lang: str = 'en'):
        """
        :param asset_class: Asset class, e.g., stocks, etf, funds, crypto, commodities, forex, indexes
        :param ticker: Ticker
        :param category: category, e.g., header, general, events, profile, statistics, earnings, insider_trades, financials, trends, filings, holders
        :param lang: Language, e.g., en, zh
        """
        url = f'https://api2.profit.com/terminal/iw/{asset_class}/{category}/{ticker}?lang={lang}'
        return self._fetch_data(url)

    def screen(self, asset_class: str, ticker: Optional[str | List[str]] = None, filters: Dict[str, Any] = None,
               sort_by: str = 'market_cap_usd', sort_order: str = 'desc', skip: Optional[int] = 0,
               limit: Optional[int] = None):
        """
        :param asset_class: Asset class, stocks
        :param ticker:
        :param filters: Filters, e.g., earning_date[gt]=2024-01-01,earning_date[lt]=2024-01-01,tags[all]=[insider_buying]
        :param sort_by: Sort by
        :param sort_order: Sort order, asc, desc
        :param skip: Number of results to skip
        :param limit: Number of results to return
        """
        # https://api2.profit.com/terminal/screener/stocks?is_popular=true
        url = f'https://api2.profit.com/terminal/screeners/{asset_class}'
        params = {}
        if filters is not None:
            for k, v in filters.items():
                params[k] = v
        if ticker is not None:
            params['ticker'] = ticker
        if skip is not None:
            params['skip'] = skip
        if limit is not None:
            params['limit'] = limit
        if sort_by is not None:
            params['sort'] = sort_by + ':' + sort_order or 'desc'
        return self._fetch_data(url, params=params)

    def screen_stocks(self, filters: Dict[str, Any] = None, ticker: Optional[str | List[str]] = None,
                      sort_by: str = 'market_cap_usd', sort_order: str = 'desc', skip: Optional[int] = 0,
                      limit: Optional[int] = None):
        """
        :param filters: Filters
        :param ticker: Ticker
        :param sort_by: Sort by
        :param sort_order: Sort order, asc, desc
        :param skip: Number of results to skip
        :param limit: Number of results to return
        """
        return self.screen('stocks', ticker, filters, sort_by, sort_order, skip, limit)

    def screen_etf(self, filters: Dict[str, Any] = None, ticker: Optional[str | List[str]] = None,
                   sort_by: str = 'net_assets',
                   sort_order: str = 'desc', skip: Optional[int] = 0, limit: Optional[int] = None):
        """
        :param filters: Filters, net_assets[gt]=1000000000
        :param ticker: Ticker
        :param sort_by: Sort by
        :param sort_order: Sort order, asc, desc
        :param skip: Number of results to skip
        :param limit: Number of results to return
        """
        # https://api2.profit.com/terminal/screeners/etf?field=&ticker=VTI.US
        return self.screen('etf', ticker, filters, sort_by, sort_order, skip, limit)

    def screen_funds(self, filters: Dict[str, Any] = None, ticker: Optional[str | List[str]] = None,
                     sort_by: str = 'net_assets',
                     sort_order: str = 'desc', skip: Optional[int] = 0, limit: Optional[int] = None):
        """
        :param filters: Filters, net_assets[gt]=1000000000
        :param ticker: Ticker
        :param sort_by: Sort by
        :param sort_order: Sort order, asc, desc
        :param skip: Number of results to skip
        :param limit: Number of results to return
        """
        # high momentum: technical_indicators__rsi_14_daily[gt]=70
        # technical_indicators.rsi_14_daily
        return self.screen('funds', ticker, filters, sort_by, sort_order, skip, limit)

    def screen_crypto(self, filters: Dict[str, Any] = None, ticker: Optional[str | List[str]] = None,
                      sort_by: str = 'market_cap_usd', sort_order: str = 'desc', skip: Optional[int] = 0,
                      limit: Optional[int] = None):
        """
        :param filters: Filters, e.g., rank[gt]=0
        :param ticker: Ticker
        :param sort_by: Sort by
        :param sort_order: Sort order, asc, desc
        :param skip: Number of results to skip
        :param limit: Number of results to return
        """
        return self.screen('crypto', ticker, filters, sort_by, sort_order, skip, limit)

    def screen_forex(self, filters: Dict[str, Any] = None, ticker: Optional[str | List[str]] = None,
                     sort_by: str = 'symbol',
                     sort_order: str = 'asc', skip: Optional[int] = 0, limit: Optional[int] = None):
        """
        :param filters: Filters, e.g., tags[all]=popular
        :param ticker: Ticker
        :param sort_by: Sort by
        :param sort_order: Sort order, asc, desc
        :param skip: Number of results to skip
        :param limit: Number of results to return
        """
        return self.screen('forex', ticker, filters, sort_by, sort_order, skip, limit)

    def screen_indices(self, filters: Dict[str, Any] = None, ticker: Optional[str | List[str]] = None,
                       sort_by: str = 'symbol',
                       sort_order: str = 'desc', skip: Optional[int] = 0, limit: Optional[int] = None):
        """
        :param filters: Filters, e.g., tags[all]=popular
        :param ticker: Ticker
        :param sort_by: Sort by
        :param sort_order: Sort order, asc, desc
        :param skip: Number of results to skip
        :param limit: Number of results to return
        """
        return self.screen('indexes', ticker, filters, sort_by, sort_order, skip, limit)

    def screen_commodities(self, filters: Dict[str, Any] = None, ticker: Optional[str | List[str]] = None,
                           sort_by: str = 'priority', sort_order: str = 'desc', skip: Optional[int] = 0,
                           limit: Optional[int] = None):
        """
        :param filters: Filters
        :param ticker: Ticker
        :param sort_by: Sort by
        :param sort_order: Sort order, asc, desc
        :param skip: Number of results to skip
        :param limit: Number of results to return
        """
        return self.screen('commodities', ticker, filters, sort_by, sort_order, skip, limit)

    def get_screener_field_values(self, asset_class: str, field: str, filters: Dict[str, Any] = None,
                                  ticker: Optional[str | List[str]] = None):
        """
        :param asset_class: Asset class, stocks
        :param field: Field, e.g., sector, industry, region, country, exchange, currency
        :param filters: Filters
        :param ticker: Ticker
        """
        url = f'https://api2.profit.com/terminal/screener/{asset_class}/{field}'
        params = {}
        if filters is not None:
            for k, v in filters.items():
                params[k] = v
        if ticker is not None:
            params['ticker'] = ticker
        return self._fetch_data(url, params=params)

    def stock_screener_field_values(self, field: str, filters: Dict[str, Any] = None,
                                    ticker: Optional[str | List[str]] = None):
        """
        :param field: Field, e.g., sector, industry, region, country, exchange, currency
        :param filters: Filters
        :param ticker: Ticker
        """
        return self.get_screener_field_values('stocks', field, filters, ticker)

    def etf_screener_field_values(self, field: str, filters: Dict[str, Any] = None,
                                  ticker: Optional[str | List[str]] = None):
        """
        :param field: Field, e.g., country, category_group, category_name, morning_star_rating
        :param filters: Filters
        :param ticker: Ticker
        """
        return self.get_screener_field_values('etf', field, filters, ticker)

    def funds_screener_field_values(self, field: str, filters: Dict[str, Any] = None,
                                    ticker: Optional[str | List[str]] = None):
        """
        :param field: Field, e.g., country, category_group, category_name
        :param filters: Filters, e.g., tags[all]=popular
        :param ticker: Ticker
        """
        return self.get_screener_field_values('funds', field, filters, ticker)

    def crypto_screener_field_values(self, field: str, filters: Dict[str, Any] = None,
                                     ticker: Optional[str | List[str]] = None):
        """
        :param field: Field, e.g., proof_type, algorithm
        :param filters: Filters, e.g., rank[gt]=0
        :param ticker: Ticker
        """
        return self.get_screener_field_values('crypto', field, filters, ticker)

    def indices_screener_field_values(self, field: str, filters: Dict[str, Any] = None,
                                      ticker: Optional[str | List[str]] = None):
        """
        :param field: Field, e.g., region, country, currency
        :param filters: Filters, e.g., tags[all]=popular
        :param ticker: Ticker
        """
        return self.get_screener_field_values('indexes', field, filters, ticker)

    def get_watchlist_recommendations(self, asset_class: str, lang: str = 'en'):
        """
        :param asset_class: Asset class, stocks, etf, crypto
        :param lang: Language, e.g., en, zh
        """
        url = f'https://api2.profit.com/terminal/recommendation/watchlist?category={asset_class}&lang={lang}'
        return self._fetch_data(url)

    def get_watchlist(self, watchlist_id: str, lang: str = 'en'):
        """
        :param watchlist_id: Watchlist ID, tech_titans
        :param lang: Language, e.g., en, zh
        """
        url = f'https://api2.profit.com/terminal/recommendation/watchlists/{watchlist_id}?lang={lang}'
        return self._fetch_data(url)

    def get_forex_spot_matrix(self, oid: Optional[str] = None):
        if oid is not None:
            url = f'https://api2.profit.com/terminal/cross-rates-matrix/forex/{oid}'
        else:
            url = 'https://api2.profit.com/terminal/cross-rates-matrix/forex/list'
        return self._fetch_data(url)

    def get_correlation_matrix(self, oid: Optional[str] = None):
        url = 'https://api2.profit.com/terminal/correlation-matrix/'
        if oid is not None:
            url += f'{oid}'
        return self._fetch_data(url)
