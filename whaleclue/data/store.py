from __future__ import annotations

import os
from typing import Optional, Any


class MongoDBManager:
    """Lazy MongoDB connector. If pymongo not installed or URI missing, acts as no-op."""

    def __init__(self, uri: Optional[str] = None, db_name: Optional[str] = None):
        self._uri = uri or os.getenv("MONGODB_URI")
        self._db_name = db_name or os.getenv("MONGODB_DB", "whale_ai")
        self._client = None
        self._db = None
        try:
            from pymongo import MongoClient  # type: ignore
            if self._uri:
                self._client = MongoClient(self._uri, serverSelectionTimeoutMS=1500)
                self._db = self._client[self._db_name]
        except Exception:
            self._client = None
            self._db = None

    def collection(self, name: str) -> Any:
        return self._db[name] if self._db is not None else None


class RedisCache:
    """Lazy Redis connector. Falls back to in-memory dict when redis unavailable."""

    def __init__(self, url: Optional[str] = None):
        self._url = url or os.getenv("REDIS_URL")
        self._client = None
        try:
            import redis  # type: ignore
            if self._url:
                self._client = redis.Redis.from_url(self._url, socket_connect_timeout=1.5)
        except Exception:
            self._client = None

    def set(self, key: str, value: str, ex: Optional[int] = None) -> None:  # pragma: no cover
        if self._client:
            try:
                self._client.set(key, value, ex=ex)
                return
            except Exception:
                pass
        # fallback ignored (not used yet)

    def get(self, key: str) -> Optional[str]:  # pragma: no cover
        if self._client:
            try:
                val = self._client.get(key)
                return val.decode("utf-8") if isinstance(val, (bytes, bytearray)) else val
            except Exception:
                return None
        return None




