from __future__ import annotations

import time
from typing import Any, Optional


class InstitutionalCacheManager:
    """Simple in-memory cache with TTL. Can be replaced by Redis implementation later."""

    def __init__(self):
        self._store: dict[str, tuple[float, Any]] = {}
        self.default_ttl = 3600

    def cache_investor_data(self, key: str, data: Any, ttl: Optional[int] = None) -> None:
        expires_at = time.time() + (ttl or self.default_ttl)
        self._store[key] = (expires_at, data)

    def get_cached_investor_data(self, key: str) -> Optional[Any]:
        item = self._store.get(key)
        if not item:
            return None
        expires_at, data = item
        if time.time() > expires_at:
            self._store.pop(key, None)
            return None
        return data

    def invalidate_investor_cache(self, pattern: str) -> None:
        keys = [k for k in self._store.keys() if pattern in k]
        for k in keys:
            self._store.pop(k, None)

    def cache_analysis_result(self, analysis_key: str, result: Any, ttl: int = 7200) -> None:
        self.cache_investor_data(analysis_key, result, ttl)

    # -------- 批量操作 --------
    def cache_many(self, data_map: dict[str, Any], ttl: Optional[int] = None) -> None:
        for key, value in data_map.items():
            self.cache_investor_data(key, value, ttl)

    def get_many(self, keys: list[str]) -> dict[str, Optional[Any]]:
        return {k: self.get_cached_investor_data(k) for k in keys}


