from __future__ import annotations

from dataclasses import dataclass, field, asdict
from datetime import datetime
from typing import List, Optional, Dict, Any


@dataclass
class StockHolding:
    ticker: str
    company_name: Optional[str] = None
    cusip: Optional[str] = None
    shares: Optional[float] = None
    value: Optional[float] = None
    portfolio_ratio: Optional[float] = None
    sector: Optional[str] = None
    price: Optional[float] = None
    roi: Optional[float] = None
    change_in_shares: Optional[float] = None
    change_in_value: Optional[float] = None


@dataclass
class PortfolioMetrics:
    concentration_ratio_top10: float = 0.0
    total_market_value: float = 0.0
    positions_count: int = 0


@dataclass
class Portfolio:
    investor_slug: str
    report_date: Optional[datetime]
    filing_date: Optional[datetime]
    total_market_value: float
    stock_holdings: List[StockHolding] = field(default_factory=list)
    sector_breakdown: Dict[str, float] = field(default_factory=dict)
    portfolio_metrics: PortfolioMetrics = field(default_factory=PortfolioMetrics)
    qoq_change: Optional[float] = None
    roi: Optional[float] = None


@dataclass
class InvestorInfo:
    slug: Optional[str] = None
    cik: Optional[str] = None
    name: Optional[str] = None
    company: Optional[str] = None
    investor_type: Optional[str] = None
    description: Optional[str] = None
    logo_url: Optional[str] = None
    aum: Optional[float] = None
    founded_year: Optional[int] = None
    location: Optional[str] = None
    website: Optional[str] = None


@dataclass
class InvestorProfile:
    investor_info: InvestorInfo
    current_portfolio: Optional[Portfolio] = None
    portfolio_history: List[Portfolio] = field(default_factory=list)
    trading_history: List[Dict[str, Any]] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.utcnow)
    version: int = 1


def to_dict(obj: Any) -> Dict[str, Any]:
    return asdict(obj)


# -------- 风格分析相关 --------

@dataclass
class StyleFeatures:
    concentration_ratio: float
    sector_diversification: float
    turnover_rate: float
    value_score: float
    growth_score: float
    momentum_score: float
    quality_score: float
    market_cap_preference: str = "unknown"


@dataclass
class InvestmentStyle:
    primary_style: str
    style_confidence: float
    style_scores: Dict[str, float]


# -------- 风格演变 --------

@dataclass
class StyleTimelinePoint:
    date: datetime
    scores: Dict[str, float]
    primary_style: str


@dataclass
class StyleEvolution:
    slug: str
    timeline: List[StyleTimelinePoint]


