import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

import streamlit as st
import plotly.express as px
from pathlib import Path

# Ensure both app directory and project root are on sys.path
APP_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = APP_DIR.parent
for p in [str(PROJECT_ROOT), str(APP_DIR)]:
    if p not in sys.path:
        sys.path.insert(0, p)

from data.profit import ProfitDataClient
from ai.agent import get_insights_with_agent
from services import PortfolioAnalysisService, InvestmentStyleService
from services.investment_clue_service import InvestmentClueService
from services.investment_recommendation_service import InvestmentRecommendationService
from services.institutional_tracking_service import InstitutionalTrackingService
from services.report_service import ReportService
from services.user_settings_service import UserSettingsService
from services import InstitutionalTaggingService
from services import StyleSimilarityService
from models import Portfolio, StockHolding, PortfolioMetrics, InvestmentStyle


@st.cache_resource(show_spinner=False)
def get_profit_interface() -> ProfitDataClient:
    return ProfitDataClient()


def _get_default_investor_types() -> List[str]:
    return ["hedge_fund_manager", "corporate_insider"]


def render_sidebar(session: Dict[str, Any]):
    st.sidebar.header("机构投资分析 - 控制台")
    # 用户设置快捷区
    usvc = UserSettingsService()
    cur = usvc.load()
    with st.sidebar.expander("用户设置", expanded=False):
        risk = st.selectbox("风险偏好", ["conservative", "moderate", "aggressive"],
                            index=["conservative", "moderate", "aggressive"].index(cur.get("risk_profile", "moderate")))
        min_inst = st.number_input("最少共识机构数", min_value=1, max_value=10,
                                   value=int(cur.get("min_consensus_institutions", 2)))
        days = st.number_input("异常交易窗口(天)", min_value=7, max_value=365,
                               value=int(cur.get("anomaly_window_days", 90)))
        if st.button("保存设置"):
            usvc.save(
                {"risk_profile": risk, "min_consensus_institutions": int(min_inst), "anomaly_window_days": int(days)})
            st.success("设置已保存")
    st.sidebar.divider()
    time_window_days = st.sidebar.slider("交易窗口(天)", 7, 180, value=session.get("trade_days", 90))
    st.sidebar.caption("用于线索发现的时间窗口")

    session["trade_days"] = time_window_days


def render_investor_card(item: Dict[str, Any]):
    cols = st.columns([1, 3, 2])
    with cols[0]:
        logo = item.get("logo_url") or item.get("logo")
        if logo:
            st.image(logo, width=72)
    with cols[1]:
        st.subheader(item.get("investor") or item.get("name") or item.get("company") or item.get("slug", "未知机构"))
        st.caption(
            f"slug: {item.get('slug', '-')} | CIK: {item.get('cik', '-')} | 类型: {item.get('type') or item.get('investor_type', '-')}"
        )
        if item.get("description"):
            st.write(item["description"][:160] + ("..." if len(item["description"]) > 160 else ""))
        elif item.get("company"):
            st.write(item.get("company"))
    with cols[2]:
        slug_or_cik = item.get('slug') or item.get('cik') or ''
        key_track = f"sel_{slug_or_cik}"
        if st.button("加入对比/跟踪", key=key_track, use_container_width=True):
            tracked = st.session_state.setdefault("tracked_slugs", set())
            slug = item.get("slug") or item.get("cik")
            if slug:
                tracked.add(slug)
                st.success(f"已添加: {slug}")
        key_detail = f"detail_{slug_or_cik}"
        if st.button("查看详情", key=key_detail, use_container_width=True):
            st.session_state["route"] = "institution_detail"
            st.session_state["detail_slug"] = item.get("slug") or item.get("cik")
            st.session_state["detail_item"] = item
            try:
                st.switch_page("pages/机构详情.py")
            except Exception:
                st.experimental_rerun()


def page_search():
    st.title("机构搜索与浏览")
    iface = get_profit_interface()
    render_sidebar(st.session_state)

    # 顶部检索输入与类别切换（不放在左侧栏）
    u = UserSettingsService().load()
    col_q, col_cat, col_btn = st.columns([3, 2, 1])
    with col_q:
        query = st.text_input("搜索机构 (名称/slug/CIK)", value="")
    with col_cat:
        choice = st.radio("机构类别", ["全部", "Manager", "Insider"], horizontal=True)
    with col_btn:
        go = st.button("搜索", use_container_width=True)

    if choice == "Manager":
        investor_types = "hedge_fund_manager"
    elif choice == "Insider":
        investor_types = "corporate_insider"
    else:
        investor_types = ["hedge_fund_manager", "corporate_insider"]

    if go and (query or investor_types):
        with st.spinner("搜索中..."):
            try:
                results: List[Dict[str, Any]] = iface.search_investors(query=query, investor_type=investor_types,
                                                                         limit=50)
            except Exception as e:
                st.error(f"搜索失败: {e}")
                results = []
        if not results:
            st.info("未找到符合条件的机构")
            return
        st.success(f"共找到 {len(results)} 条结果")
        for item in results:
            with st.container(border=True):
                render_investor_card(item)

    tracked = st.session_state.get("tracked_slugs", set())
    if tracked:
        st.subheader("已跟踪/对比的机构")
        st.write(", ".join(sorted(list(tracked))))


@st.cache_data(show_spinner=False)
def _get_latest_report_date_for_slug(slug: str) -> Optional[str]:
    iface = get_profit_interface()
    try:
        dates = iface.get_investors_reporting_dates(investor_type="hedge_fund_manager", slug=slug)
        if isinstance(dates, dict) and dates.get("reporting_dates"):
            # API 可能返回 { reporting_dates: ["2024-12-31", ...] }
            return sorted(dates["reporting_dates"])[-1]
        if isinstance(dates, list) and dates:
            return sorted(dates)[-1]
    except Exception:
        return None
    return None


@st.cache_data(show_spinner=False)
def _get_reporting_dates(slug: str) -> List[str]:
    iface = get_profit_interface()
    try:
        dates = iface.get_investors_reporting_dates(investor_type="hedge_fund_manager", slug=slug)
        if isinstance(dates, dict) and dates.get("reporting_dates"):
            return sorted(dates["reporting_dates"])  # type: ignore
        if isinstance(dates, list):
            return sorted(dates)
    except Exception:
        return []
    return []


def _collect_portfolios(slugs: List[str], report_date: Optional[str]) -> Dict[str, Any]:
    iface = get_profit_interface()
    portfolios: Dict[str, Any] = {}
    for slug in slugs:
        try:
            data = iface.get_investor_portfolio(slug=slug, report_date=report_date)
            portfolios[slug] = data
        except Exception as e:
            portfolios[slug] = {"error": str(e)}
    return portfolios


def _consensus_from_portfolios(portfolios: Dict[str, Any], min_institutions: int = 2) -> List[Dict[str, Any]]:
    ticker_to_info: Dict[str, Dict[str, Any]] = {}
    for slug, p in portfolios.items():
        if not isinstance(p, dict):
            continue
        holdings = (
            p.get("stock_holdings")
            or p.get("holdings")
            or p.get("positions")
            or []
        )
        for h in holdings:
            ticker = h.get("ticker") or h.get("symbol")
            if not ticker:
                continue
            entry = ticker_to_info.setdefault(
                ticker,
                {"ticker": ticker, "institutions": set(), "total_value": 0.0, "avg_weight": 0.0, "count": 0},
            )
            entry["institutions"].add(slug)
            value = float(h.get("value") or h.get("market_value") or 0.0)
            weight = float(h.get("portfolio_ratio") or h.get("weight") or 0.0)
            entry["total_value"] += value
            entry["avg_weight"] += weight
            entry["count"] += 1

    picks: List[Dict[str, Any]] = []
    for t, info in ticker_to_info.items():
        inst_count = len(info["institutions"])
        if inst_count >= min_institutions:
            picks.append(
                {
                    "ticker": t,
                    "institution_count": inst_count,
                    "total_value": info["total_value"],
                    "avg_portfolio_weight": (info["avg_weight"] / max(info["count"], 1)),
                    "institutions": sorted(list(info["institutions"]))
                }
            )
    picks.sort(key=lambda x: (x["institution_count"], x["total_value"]), reverse=True)
    return picks[:30]


def _unusual_trades(slugs: List[str], start_date: datetime) -> List[Dict[str, Any]]:
    iface = get_profit_interface()
    results: List[Dict[str, Any]] = []
    for slug in slugs:
        try:
            tr = iface.get_investor_trades(
                slug=slug,
                start_date=start_date.strftime("%Y-%m-%d"),
                end_date=datetime.utcnow().strftime("%Y-%m-%d"),
                limit=200,
                fetch_all=False,
            )
            trades = tr or []
            if not isinstance(trades, list):
                continue
            values = [float(t.get("trade_value") or t.get("value") or 0.0) for t in trades]
            if not values:
                continue
            threshold = max(sorted(values)[-max(1, len(values) // 10)], 0.0)  # Top 10% 作为阈值
            for t in trades:
                val = float(t.get("trade_value") or t.get("value") or 0.0)
                if val >= threshold and val > 0:
                    results.append(
                        {
                            "investor_slug": slug,
                            "ticker": t.get("ticker") or t.get("symbol"),
                            "trade_type": t.get("trade_type") or t.get("type"),
                            "trade_value": val,
                            "trade_date": t.get("trade_date") or t.get("date"),
                        }
                    )
        except Exception:
            continue
    results.sort(key=lambda x: x["trade_value"], reverse=True)
    return results[:100]


def page_clues():
    st.title("投资线索发现")
    tracked = st.session_state.get("tracked_slugs", set())
    if not tracked:
        st.info("请先在“机构搜索与浏览”页选择机构，并加入对比/跟踪")
        return

    st.write("本页聚焦共识股票与异常交易，帮助发现线索")

    usvc = UserSettingsService()
    u = usvc.load()
    min_inst = st.slider("共识最少机构数", 1, max(2, len(tracked)), int(u.get("min_consensus_institutions", 2)))
    days = int(u.get("anomaly_window_days", st.session_state.get("trade_days", 90)))
    left, right = st.columns(2)
    with left:
        report_date_opt = st.text_input("报告期(YYYY-MM-DD，可留空取最近)", value="")
    with right:
        start_date = datetime.utcnow() - timedelta(days=days)
        st.caption(f"交易窗口: 自 {start_date.date()} 至今")

    report_date = report_date_opt.strip() or None
    if report_date is None:
        # 取第一个 slug 的最近报告期作为参考
        any_slug = sorted(list(tracked))[0]
        report_date = _get_latest_report_date_for_slug(any_slug)

    st.write(f"使用报告期: {report_date or '最新可用'}")

    with st.spinner("获取组合并计算共识..."):
        portfolios = _collect_portfolios(sorted(list(tracked)), report_date)
        clue_srv = InvestmentClueService()
        picks = clue_srv.find_consensus_picks(portfolios, min_institutions=min_inst)

    st.subheader("机构共识股票（按强度排序）")
    if not picks:
        st.info("暂无共识结果")
    else:
        for p in picks[:20]:
            with st.container(border=True):
                strength = p.get('consensus_strength')
                strength_txt = f" | 强度: {strength:.2f}" if strength is not None else ""
                st.write(
                    f"{p['ticker']} | 机构数: {p['institution_count']} | 平均权重: {p['avg_portfolio_weight']:.2f}% | 总持有额: {p['total_value']:.0f}{strength_txt}")
                with st.expander("详情"):
                    st.write("相关机构：" + ", ".join(p.get("institutions", [])))
                    if strength is not None:
                        st.progress(min(max(strength, 0.0), 1.0))

    with st.spinner("检测异常交易..."):
        anomalies = _unusual_trades(sorted(list(tracked)), start_date)
        anomalies = InvestmentClueService().detect_unusual_trades(anomalies)

    st.subheader("异常交易（按异常分数排序）")
    if not anomalies:
        st.info("暂无异常交易")
    else:
        for t in anomalies[:30]:
            with st.container(border=True):
                score = t.get('anomaly_score')
                score_txt = f" | 异常分数: {score:.2f}" if score is not None else ""
                st.write(
                    f"{t['trade_date']} | {t['investor_slug']} -> {t['ticker']} | {t['trade_type']} | 金额: {t['trade_value']:.0f}{score_txt}")
                with st.expander("详情"):
                    if score is not None:
                        st.progress(min(max(score, 0.0), 1.0))
                    st.write(
                        {k: t.get(k) for k in ["investor_slug", "ticker", "trade_type", "trade_value", "trade_date"]})

    smart_moves = InvestmentClueService().detect_smart_money_moves(anomalies=anomalies, consensus=picks)
    if smart_moves:
        st.subheader("聪明钱动向（简版）")
        for m in smart_moves[:20]:
            with st.container(border=True):
                st.write(
                    f"{m['ticker']} | 机构数: {m['institution_count']} | 异常规模: {m['anomaly_size']:.0f} | 平均权重: {m['avg_weight']:.2f}%")
                st.caption("机构: " + ", ".join(m.get("institutions", [])))

    if st.button("使用 AI 生成洞见与建议", type="primary"):
        with st.spinner("AI 正在分析..."):
            insights = get_insights_with_agent(picks=picks, anomalies=anomalies)
        st.markdown(insights)


def _to_portfolio(slug: str, raw: Dict[str, Any]) -> Optional[Portfolio]:
    if not isinstance(raw, dict) or not raw:
        return None
    items = raw.get("stock_holdings") or raw.get("holdings") or raw.get("positions") or []
    holdings: List[StockHolding] = []
    for h in items:
        holdings.append(
            StockHolding(
                ticker=h.get("ticker") or h.get("symbol"),
                company_name=h.get("company_name") or h.get("name"),
                cusip=h.get("cusip"),
                shares=(h.get("shares") or h.get("qty") or 0),
                value=(h.get("value") or h.get("market_value") or 0.0),
                portfolio_ratio=(h.get("portfolio_ratio") or h.get("weight") or 0.0),
                sector=h.get("sector"),
                price=h.get("price") or None,
                roi=h.get("roi") or None,
                change_in_shares=h.get("change_in_shares") or None,
                change_in_value=h.get("change_in_value") or None,
            )
        )
    total_mv = float(raw.get("total_market_value") or raw.get("portfolio_value") or 0.0)
    report_date = raw.get("report_date") or raw.get("as_of_date")
    filing_date = raw.get("filing_date") or raw.get("filed_at")
    metrics = PortfolioMetrics(
        concentration_ratio_top10=sum(sorted([float(h.portfolio_ratio or 0.0) for h in holdings], reverse=True)[:10]),
        total_market_value=total_mv,
        positions_count=len(holdings),
    )
    # 计算行业占比
    sector_sum: Dict[str, float] = {}
    total_val = 0.0
    for h in holdings:
        v = float(h.value or 0.0)
        total_val += v
        if h.sector:
            sector_sum[h.sector] = sector_sum.get(h.sector, 0.0) + v
    sector_breakdown = {k: (v / total_val if total_val > 0 else 0.0) for k, v in sector_sum.items()}
    return Portfolio(
        investor_slug=slug,
        report_date=datetime.fromisoformat(report_date) if isinstance(report_date, str) else None,
        filing_date=datetime.fromisoformat(filing_date) if isinstance(filing_date, str) else None,
        total_market_value=total_mv,
        stock_holdings=holdings,
        sector_breakdown=sector_breakdown,
        portfolio_metrics=metrics,
    )


def page_portfolio_analysis():
    st.title("持仓分析")
    tracked = st.session_state.get("tracked_slugs", set())
    if not tracked:
        st.info("请先在“机构搜索与浏览”页选择机构，并加入对比/跟踪")
        return
    iface = get_profit_interface()
    pas = PortfolioAnalysisService()

    slug = st.selectbox("选择机构", sorted(list(tracked)))
    dates = _get_reporting_dates(slug)
    if not dates:
        st.warning("未获取到报告期，尝试使用最近一个")
    cur_date = st.selectbox("当前报告期", options=dates[::-1] if dates else [""], index=0)
    prev_date = st.selectbox("对比报告期", options=dates[::-1][1:] if dates and len(dates) > 1 else [""], index=0)

    if st.button("分析", type="primary"):
        with st.spinner("获取组合并分析..."):
            cur_raw = iface.get_investor_portfolio(slug=slug, report_date=(cur_date or None))
            prev_raw = iface.get_investor_portfolio(slug=slug,
                                                         report_date=(prev_date or None)) if prev_date else None
            cur_p = _to_portfolio(slug, cur_raw) if cur_raw else None
            prev_p = _to_portfolio(slug, prev_raw) if prev_raw else None

        if not cur_p:
            st.error("未获取到当前组合数据")
            return

        st.subheader("行业配置")
        breakdown = pas.analyze_portfolio_composition(cur_p)
        if breakdown:
            df_data = [(k, v) for k, v in breakdown.items()]
            fig = px.pie(names=[k for k, _ in df_data], values=[v for _, v in df_data], title="行业占比")
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("暂无行业数据")

        st.subheader("Top 持仓")
        top_holdings = sorted(cur_p.stock_holdings, key=lambda h: float(h.value or 0.0), reverse=True)[:20]
        st.dataframe(
            [
                {
                    "Ticker": h.ticker,
                    "名称": h.company_name,
                    "市值": h.value,
                    "权重%": h.portfolio_ratio,
                    "行业": h.sector,
                }
                for h in top_holdings
            ],
            use_container_width=True,
        )

        st.subheader("持仓变化（权重变动）")
        changes = pas.detect_portfolio_changes(cur_p, prev_p)
        cols = st.columns(2)
        with cols[0]:
            st.write("新增持仓")
            st.dataframe(
                [{"Ticker": t, "当前权重%": w} for t, w in
                 sorted(changes["new_positions"].items(), key=lambda x: x[1], reverse=True)],
                use_container_width=True,
            )
            st.write("增持")
            st.dataframe(
                [{"Ticker": t, "权重Δ%": w} for t, w in
                 sorted(changes["increased"].items(), key=lambda x: x[1], reverse=True)],
                use_container_width=True,
            )
        with cols[1]:
            st.write("减持")
            st.dataframe(
                [{"Ticker": t, "权重Δ%": w} for t, w in sorted(changes["decreased"].items(), key=lambda x: x[1])],
                use_container_width=True,
            )
            st.write("清仓")
            st.dataframe(
                [{"Ticker": t, "之前权重%": w} for t, w in
                 sorted(changes["closed"].items(), key=lambda x: x[1], reverse=True)],
                use_container_width=True,
            )


def page_style_analysis():
    st.title("投资风格分析")
    tracked = st.session_state.get("tracked_slugs", set())
    if not tracked:
        st.info("请先在“机构搜索与浏览”页选择机构，并加入对比/跟踪")
        return
    iface = get_profit_interface()
    iss = InvestmentStyleService()

    slug = st.selectbox("选择机构", sorted(list(tracked)), key="style_slug")
    dates = _get_reporting_dates(slug)
    rdate = st.selectbox("报告期", options=dates[::-1] if dates else [""], index=0, key="style_date")

    if st.button("分析风格", type="primary"):
        with st.spinner("获取组合并提取风格特征..."):
            raw = iface.get_investor_portfolio(slug=slug, report_date=(rdate or None))
            p = _to_portfolio(slug, raw) if raw else None
        if not p:
            st.error("未获取到组合数据")
            return
        feats = iss.extract_style_features(p)
        style = iss.classify_investment_style(feats)
        st.subheader("风格结论")
        st.write(f"主要风格：{style.primary_style} | 置信度：{style.style_confidence:.2f}")
        # 生成标签
        tags = InstitutionalTaggingService().generate_style_tags(style)
        if tags:
            st.caption("标签: " + ", ".join(t['tag_name'] for t in tags))
        st.subheader("风格分数")
        radar_scores = InvestmentStyleService.style_scores_for_radar(style)
        st.dataframe([{"风格": k, "分数": v} for k, v in radar_scores.items()], use_container_width=True)
        try:
            import pandas as pd
            import plotly.express as px
            df = pd.DataFrame({"维度": list(radar_scores.keys()), "分数": list(radar_scores.values())})
            fig = px.line_polar(df, r="分数", theta="维度", line_close=True)
            fig.update_traces(fill='toself')
            st.plotly_chart(fig, use_container_width=True)
        except Exception:
            pass

        # 演变趋势（若有多个报告期）
        dates = _get_reporting_dates(slug)
        if dates and len(dates) >= 3:
            with st.spinner("计算风格演变..."):
                recent = dates[-6:]  # 取近6期
                portfolios = {}
                for d in recent:
                    raw_p = iface.get_investor_portfolio(slug=slug, report_date=d)
                    p2 = _to_portfolio(slug, raw_p)
                    if p2:
                        portfolios[d] = p2
                evo = iss.build_style_evolution(slug, portfolios)
            try:
                import pandas as pd
                import plotly.express as px
                rows = []
                for pt in evo.timeline:
                    for k, v in InvestmentStyleService.style_scores_for_radar(
                        InvestmentStyle(primary_style=pt.primary_style, style_confidence=1.0,
                                        style_scores=pt.scores)).items():
                        rows.append({"报告期": pt.date.strftime("%Y-%m-%d") if pt.date else "", "风格": k, "分数": v})
                df = pd.DataFrame(rows)
                fig = px.line(df, x="报告期", y="分数", color="风格")
                st.subheader("风格演变趋势（近6期）")
                st.plotly_chart(fig, use_container_width=True)
            except Exception:
                pass


def page_style_compare():
    st.title("风格对比与相似机构")
    tracked = st.session_state.get("tracked_slugs", set())
    if not tracked:
        st.info("请先在“机构搜索与浏览”页选择机构，并加入对比/跟踪")
        return
    iface = get_profit_interface()
    iss = InvestmentStyleService()
    slugs = sorted(list(tracked))
    target = st.selectbox("选择基准机构", slugs)
    dates = _get_reporting_dates(target)
    rdate = st.selectbox("报告期", options=dates[::-1] if dates else [""], index=0)

    if st.button("计算相似机构"):
        with st.spinner("提取风格并计算相似度..."):
            styles: dict[str, InvestmentStyle] = {}
            for slug in slugs:
                raw = iface.get_investor_portfolio(slug=slug, report_date=(rdate or None))
                p = _to_portfolio(slug, raw) if raw else None
                if not p:
                    continue
                feats = iss.extract_style_features(p)
                styles[slug] = iss.classify_investment_style(feats)
            sims = StyleSimilarityService().top_similar(target, styles, top_k=10)
        if not sims:
            st.info("无相似结果")
            return
        st.subheader("最相似机构（按风格分数余弦相似度）")
        st.dataframe([{"机构": slug, "相似度": f"{sim:.2f}"} for slug, sim in sims], use_container_width=True)


def page_institution_detail():
    st.title("机构详情")
    iface = get_profit_interface()
    slug = st.session_state.get("detail_slug")
    item = st.session_state.get("detail_item") or {}
    if not slug:
        st.info("缺少机构标识")
        return
    # 头部卡片
    col1, col2 = st.columns([1, 3])
    with col1:
        if item.get("logo_url"):
            st.image(item["logo_url"], width=96)
    with col2:
        st.subheader(item.get("investor") or item.get("name") or item.get("company") or slug)
        st.caption(f"slug: {slug} | CIK: {item.get('cik', '-')}")
        if item.get("description"):
            st.write(item["description"])

    # 报告期选择与当前组合
    dates = _get_reporting_dates(slug)
    rdate = st.selectbox("报告期", options=dates[::-1] if dates else [""], index=0)
    with st.spinner("加载持仓..."):
        port = iface.get_investor_portfolio(slug=slug, report_date=(rdate or None))
    if not isinstance(port, dict) or not port:
        st.info("暂无持仓数据")
        return
    # Top 持仓概览
    items = port.get("stock_holdings") or port.get("holdings") or port.get("positions") or []
    top = sorted(items, key=lambda h: float(h.get("value") or h.get("market_value") or 0.0), reverse=True)[:20]
    st.subheader("Top 持仓")
    st.dataframe([
        {
            "Ticker": h.get("ticker") or h.get("symbol"),
            "名称": h.get("company_name") or h.get("name"),
            "市值": h.get("value") or h.get("market_value"),
            "权重%": h.get("portfolio_ratio") or h.get("weight"),
            "行业": h.get("sector"),
        } for h in top
    ], use_container_width=True)


def page_recommendations():
    st.title("投资建议")
    tracked = st.session_state.get("tracked_slugs", set())
    if not tracked:
        st.info("请先在“机构搜索与浏览”页选择机构，并加入对比/跟踪")
        return

    iface = get_profit_interface()
    slugs = sorted(list(tracked))
    usvc = UserSettingsService()
    u = usvc.load()
    min_inst = st.slider("共识最少机构数", 1, max(2, len(slugs)), int(u.get("min_consensus_institutions", 2)),
                         key="rec_min_inst")
    report_date_opt = st.text_input("报告期(YYYY-MM-DD，可留空取最近)", value="", key="rec_report")
    days = st.slider("异常交易窗口(天)", 7, 365, int(u.get("anomaly_window_days", 90)), key="rec_days")
    risk = st.selectbox("风险偏好", ["conservative", "moderate", "aggressive"],
                        index=["conservative", "moderate", "aggressive"].index(u.get("risk_profile", "moderate")))

    if st.button("生成建议", type="primary"):
        with st.spinner("汇总线索..."):
            report_date = report_date_opt.strip() or None
            if report_date is None:
                report_date = _get_latest_report_date_for_slug(slugs[0])
            portfolios = _collect_portfolios(slugs, report_date)
            clue_srv = InvestmentClueService()
            consensus = clue_srv.find_consensus_picks(portfolios, min_institutions=min_inst)
            anomalies = _unusual_trades(slugs, datetime.utcnow() - timedelta(days=days))
            anomalies = clue_srv.detect_unusual_trades(anomalies)

        with st.spinner("生成建议..."):
            rec_srv = InvestmentRecommendationService()
            recs = rec_srv.generate_recommendations(consensus=consensus, anomalies=anomalies, style=None, top_k=10,
                                                    risk_profile=risk)

        if not recs:
            st.info("暂无建议")
            return
        for r in recs:
            with st.container(border=True):
                st.subheader(r["ticker"])
                st.caption(f"推荐分数: {r['score']:.2f} | 机构: {len(r.get('institutions', []))}")
                st.write("建议逻辑：")
                for i, line in enumerate(r["rationale"], 1):
                    st.write(f"{i}. {line}")
                st.write("潜在风险：")
                for i, line in enumerate(r["risks"], 1):
                    st.write(f"{i}. {line}")
                st.write(f"建议持有期限：{r['horizon']}")


def page_report():
    st.title("报告生成")
    tracked = st.session_state.get("tracked_slugs", set())
    if not tracked:
        st.info("请先在“机构搜索与浏览”页选择机构，并加入对比/跟踪")
        return
    iface = get_profit_interface()
    slugs = sorted(list(tracked))
    min_inst = st.slider("共识最少机构数", 2, max(2, len(slugs)), 2, key="rep_min_inst")
    report_date_opt = st.text_input("报告期(YYYY-MM-DD，可留空取最近)", value="", key="rep_report")
    days = st.slider("异常交易窗口(天)", 7, 180, 90, key="rep_days")
    title = st.text_input("报告标题", value="机构投资线索与建议报告")

    if st.button("生成HTML报告"):
        with st.spinner("汇总数据..."):
            report_date = report_date_opt.strip() or None
            if report_date is None:
                report_date = _get_latest_report_date_for_slug(slugs[0])
            portfolios = _collect_portfolios(slugs, report_date)
            clue_srv = InvestmentClueService()
            consensus = clue_srv.find_consensus_picks(portfolios, min_institutions=min_inst)
            anomalies = _unusual_trades(slugs, datetime.utcnow() - timedelta(days=days))
            anomalies = clue_srv.detect_unusual_trades(anomalies)
            rec_srv = InvestmentRecommendationService()
            recs = rec_srv.generate_recommendations(consensus=consensus, anomalies=anomalies, style=None, top_k=10)

        html = ReportService().build_html_report(title=title, consensus=consensus, anomalies=anomalies,
                                                 recommendations=recs)
        st.download_button(
            label="下载HTML报告",
            data=html,
            file_name=f"report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.html",
            mime="text/html",
        )


def page_tracking():
    st.title("机构跟踪")
    iface = get_profit_interface()
    tsvc = InstitutionalTrackingService(iface)
    current_list = set(tsvc.load_watchlist())
    tracked = set(st.session_state.get("tracked_slugs", set()))
    merged = sorted(list(current_list.union(tracked)))
    sel = st.multiselect("跟踪机构列表", options=merged, default=merged)
    if st.button("保存列表"):
        tsvc.save_watchlist(sel)
        st.success("已保存")

    if not sel:
        st.info("添加一些机构到跟踪列表以查看时间线和持仓变化")
        return

    days = st.slider("交易时间线窗口(天)", 7, 120, 30)
    for slug in sel:
        with st.expander(f"{slug} 最近动态", expanded=False):
            trades = tsvc.get_recent_trades(slug, days_back=days)
            if trades:
                st.write("最近交易：")
                st.dataframe([
                    {
                        "日期": t.get("trade_date") or t.get("date"),
                        "Ticker": t.get("ticker") or t.get("symbol"),
                        "类型": t.get("trade_type") or t.get("type"),
                        "金额": t.get("trade_value") or t.get("value"),
                    } for t in trades[:50]
                ], use_container_width=True)
            else:
                st.caption("近期无交易")

            changes = tsvc.get_latest_portfolio_changes(slug)
            if changes:
                st.write(f"持仓变化：{changes['previous']} -> {changes['current']}")
                c1, c2 = st.columns(2)
                with c1:
                    st.write("新增/增持")
                    st.dataframe(
                        [{"Ticker": t, "权重(或Δ%)": w} for t, w in
                         {**changes["new_positions"], **changes["increased"]}.items()],
                        use_container_width=True,
                    )
                with c2:
                    st.write("减持/清仓")
                    st.dataframe(
                        [{"Ticker": t, "权重(或Δ%)": w} for t, w in
                         {**changes["decreased"], **changes["closed"]}.items()],
                        use_container_width=True,
                    )
            else:
                st.caption("缺少对比报告期，无法展示持仓变化")


def main():
    st.set_page_config(page_title="机构投资线索发现", layout="wide")
    route = st.session_state.get("route", "tabs")
    if route == "institution_detail":
        page_institution_detail()
        if st.button("返回", type="secondary"):
            st.session_state["route"] = "tabs"
            st.experimental_rerun()
        return

    tabs = st.tabs(["机构搜索与浏览", "投资线索发现", "持仓分析", "投资风格分析", "投资建议", "机构跟踪", "报告生成", "风格对比"])
    with tabs[0]:
        page_search()
    with tabs[1]:
        page_clues()
    with tabs[2]:
        page_portfolio_analysis()
    with tabs[3]:
        page_style_analysis()
    with tabs[4]:
        page_recommendations()
    with tabs[5]:
        page_tracking()
    with tabs[6]:
        page_report()
    with tabs[7]:
        page_style_compare()


if __name__ == "__main__":
    main()
