from __future__ import annotations

from datetime import datetime
from math import log1p
from typing import Dict, List, Any, Tuple


class InvestmentClueService:
    """线索发掘：异常交易、共识股票、以及简版聪明钱动向。"""

    def detect_unusual_trades(self, trades: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        if not trades:
            return []
        values = [float(t.get("trade_value") or t.get("value") or 0.0) for t in trades]
        values = [v for v in values if v > 0]
        if not values:
            return []
        values_sorted = sorted(values)
        idx = max(0, int(0.9 * (len(values_sorted) - 1)))
        threshold = values_sorted[idx]
        results: List[Dict[str, Any]] = []
        for t in trades:
            val = float(t.get("trade_value") or t.get("value") or 0.0)
            if val >= threshold and val > 0:
                # 基于分位程度与规模的简单重要性评分
                percentile = self._percentile_rank(values_sorted, val)
                anomaly_score = 0.6 * percentile + 0.4 * (log1p(val) / (log1p(values_sorted[-1]) or 1.0))
                enriched = dict(t)
                enriched["anomaly_score"] = float(anomaly_score)
                results.append(enriched)
        results.sort(key=lambda x: float(x.get("anomaly_score") or x.get("trade_value") or x.get("value") or 0.0), reverse=True)
        return results

    def find_consensus_picks(self, portfolios: Dict[str, Any], min_institutions: int = 2) -> List[Dict[str, Any]]:
        ticker_to_info: Dict[str, Dict[str, Any]] = {}
        for slug, p in portfolios.items():
            if not isinstance(p, dict):
                continue
            holdings = (
                p.get("stock_holdings")
                or p.get("holdings")
                or p.get("positions")
                or []
            )
            for h in holdings:
                ticker = h.get("ticker") or h.get("symbol")
                if not ticker:
                    continue
                entry = ticker_to_info.setdefault(
                    ticker,
                    {"ticker": ticker, "institutions": set(), "total_value": 0.0, "avg_weight": 0.0, "count": 0},
                )
                entry["institutions"].add(slug)
                value = float(h.get("value") or h.get("market_value") or 0.0)
                weight = float(h.get("portfolio_ratio") or h.get("weight") or 0.0)
                entry["total_value"] += value
                entry["avg_weight"] += weight
                entry["count"] += 1

        picks: List[Dict[str, Any]] = []
        total_inst = max(1, len({s for info in ticker_to_info.values() for s in info["institutions"]}))
        max_total_value = max([info["total_value"] for info in ticker_to_info.values()] + [1.0])
        for _, info in ticker_to_info.items():
            inst_count = len(info["institutions"])
            if inst_count >= min_institutions:
                avg_w = (info["avg_weight"] / max(info["count"], 1))
                strength = 0.5 * (inst_count / total_inst) + 0.3 * (avg_w / 100.0) + 0.2 * (log1p(info["total_value"]) / log1p(max_total_value))
                picks.append({
                    "ticker": info["ticker"],
                    "institution_count": inst_count,
                    "total_value": info["total_value"],
                    "avg_portfolio_weight": avg_w,
                    "institutions": sorted(list(info["institutions"])),
                    "consensus_strength": float(strength),
                })
        picks.sort(key=lambda x: (x.get("consensus_strength", 0.0), x["institution_count"], x["total_value"]), reverse=True)
        return picks

    def detect_smart_money_moves(
        self,
        anomalies: List[Dict[str, Any]],
        consensus: List[Dict[str, Any]],
    ) -> List[Dict[str, Any]]:
        """简版聪明钱动向：若某标的同时在异常交易与共识榜出现，则标记为重点关注。"""
        anomaly_tickers = {}
        for t in anomalies:
            ticker = t.get("ticker")
            if not ticker:
                continue
            val = float(t.get("trade_value") or t.get("value") or 0.0)
            anomaly_tickers[ticker] = max(anomaly_tickers.get(ticker, 0.0), val)

        results: List[Dict[str, Any]] = []
        for p in consensus:
            t = p.get("ticker")
            if t in anomaly_tickers:
                results.append({
                    "ticker": t,
                    "anomaly_size": anomaly_tickers[t],
                    "institution_count": p.get("institution_count"),
                    "avg_weight": p.get("avg_portfolio_weight"),
                    "total_value": p.get("total_value"),
                    "institutions": p.get("institutions", []),
                    "consensus_strength": p.get("consensus_strength"),
                })
        results.sort(key=lambda x: (x["institution_count"], x["anomaly_size"]), reverse=True)
        return results

    @staticmethod
    def _percentile_rank(sorted_values: List[float], value: float) -> float:
        if not sorted_values:
            return 0.0
        # 计算 value 在有序数组中的分位 [0,1]
        n = len(sorted_values)
        # 找到第一个大于等于 value 的位置
        left, right = 0, n - 1
        pos = n - 1
        while left <= right:
            mid = (left + right) // 2
            if sorted_values[mid] >= value:
                pos = mid
                right = mid - 1
            else:
                left = mid + 1
        return pos / max(1, n - 1)


