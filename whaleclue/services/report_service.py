from __future__ import annotations

from datetime import datetime
from typing import Any, Dict, List


class ReportService:
    """生成简单的HTML报告（后续可扩展为PDF导出）。"""

    def build_html_report(
        self,
        title: str,
        consensus: List[Dict[str, Any]],
        anomalies: List[Dict[str, Any]],
        recommendations: List[Dict[str, Any]],
        generated_at: datetime | None = None,
    ) -> str:
        ts = (generated_at or datetime.utcnow()).strftime("%Y-%m-%d %H:%M UTC")
        html = [
            "<html><head><meta charset='utf-8'><title>{}</title>".format(title),
            "<style>body{font-family:Arial,Helvetica,sans-serif;line-height:1.5;padding:24px;} h2{margin-top:28px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;} th{background:#f6f6f6;text-align:left;} .caption{color:#777;font-size:12px;}</style>",
            "</head><body>",
            f"<h1>{title}</h1>",
            f"<div class='caption'>生成时间：{ts}</div>",
        ]

        # 共识
        html.append("<h2>机构共识股票</h2>")
        if consensus:
            html.append("<table><tr><th>Ticker</th><th>机构数</th><th>平均权重(%)</th><th>总持有额</th></tr>")
            for c in consensus[:30]:
                html.append(
                    f"<tr><td>{c.get('ticker')}</td><td>{c.get('institution_count')}</td><td>{c.get('avg_portfolio_weight'):.2f}</td><td>{c.get('total_value'):.0f}</td></tr>"
                )
            html.append("</table>")
        else:
            html.append("<p>暂无共识结果</p>")

        # 异常
        html.append("<h2>异常交易</h2>")
        if anomalies:
            html.append("<table><tr><th>日期</th><th>机构</th><th>Ticker</th><th>类型</th><th>金额</th></tr>")
            for a in anomalies[:50]:
                html.append(
                    f"<tr><td>{a.get('trade_date') or a.get('date')}</td><td>{a.get('investor_slug','')}</td><td>{a.get('ticker') or a.get('symbol')}</td><td>{a.get('trade_type') or a.get('type')}</td><td>{a.get('trade_value') or a.get('value')}</td></tr>"
                )
            html.append("</table>")
        else:
            html.append("<p>暂无异常交易</p>")

        # 建议
        html.append("<h2>投资建议</h2>")
        if recommendations:
            for r in recommendations:
                html.append(f"<h3>{r.get('ticker')}</h3>")
                html.append(f"<div class='caption'>推荐分数：{r.get('score'):.2f}</div>")
                html.append("<p><b>建议逻辑：</b></p><ul>")
                for line in r.get("rationale", []):
                    html.append(f"<li>{line}</li>")
                html.append("</ul>")
                html.append("<p><b>潜在风险：</b></p><ul>")
                for line in r.get("risks", []):
                    html.append(f"<li>{line}</li>")
                html.append("</ul>")
                html.append(f"<p><b>建议持有期限：</b>{r.get('horizon')}</p>")
        else:
            html.append("<p>暂无建议</p>")

        html.append("</body></html>")
        return "".join(html)


