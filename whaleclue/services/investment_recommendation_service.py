from __future__ import annotations

from typing import Any, Dict, List, Optional
from math import log1p


class InvestmentRecommendationService:
    """基于线索与风格的轻量建议生成（启发式版）。"""

    def generate_recommendations(
        self,
        consensus: List[Dict[str, Any]],
        anomalies: List[Dict[str, Any]],
        style: Optional[Dict[str, Any]] = None,
        top_k: int = 5,
        risk_profile: str = "moderate",
    ) -> List[Dict[str, Any]]:
        # 评分：共识强度/机构数 + 平均权重 + 异常规模（若有），按风险偏好调权
        risk_profile = (risk_profile or "moderate").lower()
        if risk_profile == "conservative":
            w_cs, w_inst, w_avg, w_anom = 0.6, 0.2, 0.15, 0.05
        elif risk_profile == "aggressive":
            w_cs, w_inst, w_avg, w_anom = 0.3, 0.2, 0.2, 0.3
        else:
            w_cs, w_inst, w_avg, w_anom = 0.45, 0.25, 0.2, 0.1

        anomaly_map: Dict[str, float] = {}
        for a in anomalies:
            t = a.get("ticker")
            if not t:
                continue
            v = float(a.get("trade_value") or a.get("value") or 0.0)
            anomaly_map[t] = max(anomaly_map.get(t, 0.0), v)

        max_inst = max((int(c.get("institution_count") or 0) for c in consensus), default=1)
        max_anom = max(anomaly_map.values() or [1.0])

        recs: List[Dict[str, Any]] = []
        for c in consensus:
            t = c.get("ticker")
            if not t:
                continue
            inst = int(c.get("institution_count") or 0)
            avg_w = float(c.get("avg_portfolio_weight") or 0.0)
            anomaly = float(anomaly_map.get(t, 0.0))
            # 归一化
            inst_norm = inst / max(1, max_inst)
            avg_w_norm = max(0.0, min(1.0, avg_w / 100.0))
            anom_norm = (log1p(anomaly) / log1p(max_anom)) if anomaly > 0 else 0.0
            cs = float(c.get("consensus_strength") or (0.6 * inst_norm + 0.4 * avg_w_norm))
            score = w_cs * cs + w_inst * inst_norm + w_avg * avg_w_norm + w_anom * anom_norm

            rationale = [
                f"多机构共识（机构数 {inst}）",
                f"组合平均权重 {avg_w:.2f}%",
            ]
            if anomaly > 0:
                rationale.append(f"近期出现较大额异常交易（规模≈{anomaly:.0f}）")

            risks = [
                "行业系统性风险与流动性不足导致的回撤",
                "短期事件驱动失效或业绩不及预期",
            ]
            # 建议持有期限按风险偏好与线索类型调整
            if risk_profile == "conservative":
                horizon = "中长线（6-12个月）"
            elif risk_profile == "aggressive":
                horizon = "短期/事件驱动（1-3个月）" if anomaly > 0 else "中期（3-6个月）"
            else:
                horizon = "中期（3-6个月）"

            recs.append(
                {
                    "ticker": t,
                    "score": score,
                    "rationale": rationale,
                    "risks": risks,
                    "horizon": horizon,
                    "institutions": c.get("institutions", []),
                    "consensus_strength": cs,
                }
            )

        recs.sort(key=lambda x: x["score"], reverse=True)
        return recs[:top_k]




