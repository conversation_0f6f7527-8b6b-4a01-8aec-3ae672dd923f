from __future__ import annotations

from typing import Dict

import numpy as np

from whaleclue.models import Portfolio, StyleFeatures, InvestmentStyle, StyleEvolution, StyleTimelinePoint


class InvestmentStyleService:
    """轻量级风格识别（规则/启发式）：后续可替换为 ML 模型。"""

    def extract_style_features(self, portfolio: Portfolio) -> StyleFeatures:
        weights = np.array([float(h.portfolio_ratio or 0.0) for h in portfolio.stock_holdings], dtype=float)
        weights = weights[weights > 0]
        total_weight = float(np.sum(weights)) if weights.size > 0 else 0.0

        # 集中度（前10权重和）
        concentration_ratio = float(np.sum(np.sort(weights)[-10:])) if weights.size > 0 else 0.0

        # 行业分散度：行业权重的赫芬达尔指数倒数近似（越大越分散）
        sector_weights = np.array(list(portfolio.sector_breakdown.values()), dtype=float)
        hhi = float(np.sum(sector_weights ** 2)) if sector_weights.size > 0 else 1.0
        sector_diversification = float(1.0 / max(hhi, 1e-6))

        # 启发式行为特征（占位）
        turnover_rate = 0.0  # 需要历史持仓辅助，此处置0
        value_score = 0.5
        growth_score = 0.5
        momentum_score = 0.5
        quality_score = 0.5
        market_cap_preference = "unknown"

        return StyleFeatures(
            concentration_ratio=concentration_ratio,
            sector_diversification=sector_diversification,
            turnover_rate=turnover_rate,
            value_score=value_score,
            growth_score=growth_score,
            momentum_score=momentum_score,
            quality_score=quality_score,
            market_cap_preference=market_cap_preference,
        )

    def classify_investment_style(self, features: StyleFeatures) -> InvestmentStyle:
        # 简单规则：集中度高且质量/价值高 -> 质量/价值；成长高 -> 成长；动量高 -> 动量
        scores: Dict[str, float] = {
            "Value": 0.4 * features.value_score + 0.2 * (1 - features.turnover_rate),
            "Growth": 0.6 * features.growth_score,
            "Momentum": 0.6 * features.momentum_score + 0.2 * features.turnover_rate,
            "Quality": 0.6 * features.quality_score + 0.2 * features.concentration_ratio,
            "Balanced": 0.5 * (features.value_score + features.growth_score) / 2.0,
        }
        primary = max(scores, key=scores.get)
        confidence = float(scores[primary])
        return InvestmentStyle(primary_style=primary, style_confidence=confidence, style_scores=scores)

    # 辅助：提取可用于雷达图的风格分数序列
    @staticmethod
    def style_scores_for_radar(style: InvestmentStyle) -> Dict[str, float]:
        # 仅取核心四风格 + Balanced
        keys = ["Value", "Growth", "Momentum", "Quality", "Balanced"]
        return {k: float(style.style_scores.get(k, 0.0)) for k in keys}

    # 基于报告期序列构建风格演变（简单以组合当期特征->风格）
    def build_style_evolution(self, slug: str, portfolios: Dict[str, Portfolio]) -> StyleEvolution:
        points: list[StyleTimelinePoint] = []
        for rdate, p in sorted(portfolios.items(), key=lambda x: x[0]):
            feats = self.extract_style_features(p)
            st = self.classify_investment_style(feats)
            points.append(StyleTimelinePoint(date=p.report_date or None, scores=st.style_scores, primary_style=st.primary_style))
        return StyleEvolution(slug=slug, timeline=points)


