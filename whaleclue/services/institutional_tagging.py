from __future__ import annotations

from typing import Dict, List

from whaleclue.models import InvestmentStyle


class InstitutionalTaggingService:
    """基础标签服务：根据风格输出风格标签与简单行为标签（可扩展）。"""

    def generate_style_tags(self, style: InvestmentStyle) -> List[Dict[str, str]]:
        tags: List[Dict[str, str]] = []
        primary = style.primary_style.lower()
        tags.append({"tag_type": "style", "tag_name": primary})
        # 根据得分阈值追加辅助标签
        for k, v in style.style_scores.items():
            if v >= 0.6 and k.lower() != primary:
                tags.append({"tag_type": "style", "tag_name": k.lower()})
        return tags

    def generate_behavior_tags(self, turnover_rate: float) -> List[Dict[str, str]]:
        if turnover_rate >= 0.5:
            return [{"tag_type": "behavior", "tag_name": "high_turnover"}]
        if turnover_rate >= 0.2:
            return [{"tag_type": "behavior", "tag_name": "mid_turnover"}]
        return [{"tag_type": "behavior", "tag_name": "low_turnover"}]




