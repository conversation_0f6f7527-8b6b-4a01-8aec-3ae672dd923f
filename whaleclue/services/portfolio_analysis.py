from __future__ import annotations

import math
from typing import Dict, List, Optional

import numpy as np

from whaleclue.models import Portfolio, StockHolding, PortfolioMetrics
from typing import Optional
try:
    from profit import ProfitDataClient  # type: ignore
except Exception:  # pragma: no cover
    ProfitDataClient = None  # type: ignore


class PortfolioAnalysisService:
    """投资组合分析：构成与行业、基础风险指标计算、变化检测雏形。"""

    def __init__(self, profit_interface: Optional[ProfitDataClient] = None):
        self.api = profit_interface

    # ---- 构成分析 ----
    def analyze_portfolio_composition(self, portfolio: Portfolio) -> Dict[str, float]:
        sector_breakdown = portfolio.sector_breakdown
        if sector_breakdown:
            return sector_breakdown
        # 若未预计算，按持仓金额分摊
        sector_sum: Dict[str, float] = {}
        total = 0.0
        for h in portfolio.stock_holdings:
            v = float(h.value or 0.0)
            total += v
            if h.sector:
                sector_sum[h.sector] = sector_sum.get(h.sector, 0.0) + v
        if total <= 0:
            return {}
        return {k: v / total for k, v in sector_sum.items()}

    # ---- 风险指标 ----
    def calculate_risk_metrics(self, returns: List[float], rf: float = 0.0) -> Dict[str, float]:
        if not returns:
            return {"vol": 0.0, "sharpe": 0.0, "max_drawdown": 0.0}
        arr = np.array(returns, dtype=float)
        vol = float(np.std(arr, ddof=1)) if len(arr) > 1 else 0.0
        mean = float(np.mean(arr))
        sharpe = (mean - rf) / vol if vol > 1e-12 else 0.0
        # 最大回撤
        cum = np.cumsum(arr)
        peak = np.maximum.accumulate(cum)
        drawdown = cum - peak
        max_dd = float(np.min(drawdown)) if len(drawdown) else 0.0
        return {"vol": vol, "sharpe": sharpe, "max_drawdown": max_dd}

    # ---- 变化检测（简版）----
    def detect_portfolio_changes(self, current: Portfolio, previous: Optional[Portfolio]) -> Dict[str, Dict[str, float]]:
        if previous is None:
            return {"new_positions": {}, "increased": {}, "decreased": {}, "closed": {}}

        def to_map(p: Portfolio) -> Dict[str, float]:
            m: Dict[str, float] = {}
            for h in p.stock_holdings:
                if h.ticker:
                    m[h.ticker] = float(h.portfolio_ratio or 0.0)
            return m

        cur = to_map(current)
        prev = to_map(previous)
        new_positions = {t: w for t, w in cur.items() if t not in prev}
        closed = {t: w for t, w in prev.items() if t not in cur}
        increased: Dict[str, float] = {}
        decreased: Dict[str, float] = {}
        for t, w in cur.items():
            if t in prev:
                delta = w - prev[t]
                if delta > 0:
                    increased[t] = delta
                elif delta < 0:
                    decreased[t] = delta
        return {"new_positions": new_positions, "increased": increased, "decreased": decreased, "closed": closed}




