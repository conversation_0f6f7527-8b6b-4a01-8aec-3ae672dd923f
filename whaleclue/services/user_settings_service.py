from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict


DEFAULT_SETTINGS: Dict[str, Any] = {
    "risk_profile": "moderate",  # conservative | moderate | aggressive
    "preferred_investor_types": ["hedge_fund_manager", "corporate_insider"],
    "min_consensus_institutions": 2,
    "anomaly_window_days": 90,
}


class UserSettingsService:
    """File-based user settings service."""

    def __init__(self, storage_dir: Path | None = None):
        self.storage_dir = storage_dir or Path(__file__).resolve().parents[1]
        self.settings_path = self.storage_dir / "user_settings.json"

    def load(self) -> Dict[str, Any]:
        try:
            if self.settings_path.exists():
                data = json.loads(self.settings_path.read_text("utf-8"))
                return {**DEFAULT_SETTINGS, **(data or {})}
        except Exception:
            pass
        return dict(DEFAULT_SETTINGS)

    def save(self, settings: Dict[str, Any]) -> None:
        data = {**DEFAULT_SETTINGS, **(settings or {})}
        try:
            self.settings_path.write_text(json.dumps(data, ensure_ascii=False, indent=2), encoding="utf-8")
        except Exception:
            pass


