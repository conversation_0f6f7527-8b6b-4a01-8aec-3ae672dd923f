from __future__ import annotations

from datetime import datetime
from typing import List, Optional, Dict, Any

from whaleclue.data.profit import ProfitDataClient
from whaleclue.data.store import MongoDBManager
from whaleclue.models import InvestorProfile, InvestorInfo, Portfolio, StockHolding, PortfolioMetrics, to_dict


class InstitutionalDataService:
    """面向上层的机构数据服务：同步、清洗、入库与读取。"""

    def __init__(self, profit_interface: ProfitDataClient, db: Optional[MongoDBManager] = None):
        self.profit_api = profit_interface
        self.db = db or MongoDBManager()

    # -------- 同步入口 --------
    def sync_investor_data(self, slug: str) -> InvestorProfile:
        """同步单个机构的数据：基础信息、最近组合、交易历史。"""
        # 基础信息（从搜索/组合补齐）
        info = InvestorInfo(slug=slug)

        # 最近报告期
        report_date = self._get_latest_report_date(slug)

        # 投资组合
        portfolio_raw = self.profit_api.get_investor_portfolio(slug=slug, report_date=report_date)
        portfolio = self._normalize_portfolio(slug, portfolio_raw)

        # 交易历史（近90天）
        trades = self.profit_api.get_investor_trades(
            slug=slug, start_date=(datetime.utcnow().date().replace(day=1)).isoformat(), limit=200
        )

        profile = InvestorProfile(
            investor_info=info,
            current_portfolio=portfolio,
            portfolio_history=[portfolio] if portfolio else [],
            trading_history=trades or [],
        )

        self._save_profile(profile)
        return profile

    def get_investor_profile(self, slug: str) -> Optional[InvestorProfile]:
        col = self._col("investor_profiles")
        if col is None:
            return None
        doc = col.find_one({"investor_info.slug": slug}) if col else None
        if not doc:
            return None
        return self._from_doc(doc)

    # -------- 内部方法 --------
    def _get_latest_report_date(self, slug: str) -> Optional[str]:
        try:
            dates = self.profit_api.get_investors_reporting_dates(
                investor_type="hedge_fund_manager", slug=slug
            )
            if isinstance(dates, dict) and dates.get("reporting_dates"):
                return sorted(dates["reporting_dates"])[-1]
            if isinstance(dates, list) and dates:
                return sorted(dates)[-1]
        except Exception:
            return None
        return None

    def _normalize_portfolio(self, slug: str, raw: Dict[str, Any]) -> Optional[Portfolio]:
        if not isinstance(raw, dict) or not raw:
            return None
        holdings = []
        items = raw.get("stock_holdings") or raw.get("holdings") or raw.get("positions") or []
        for h in items:
            holdings.append(
                StockHolding(
                    ticker=h.get("ticker") or h.get("symbol"),
                    company_name=h.get("company_name") or h.get("name"),
                    cusip=h.get("cusip"),
                    shares=(h.get("shares") or h.get("qty") or 0),
                    value=(h.get("value") or h.get("market_value") or 0.0),
                    portfolio_ratio=(h.get("portfolio_ratio") or h.get("weight") or 0.0),
                    sector=h.get("sector"),
                    price=h.get("price") or None,
                    roi=h.get("roi") or None,
                    change_in_shares=h.get("change_in_shares") or None,
                    change_in_value=h.get("change_in_value") or None,
                )
            )
        total_mv = float(raw.get("total_market_value") or raw.get("portfolio_value") or 0.0)
        report_date = raw.get("report_date") or raw.get("as_of_date")
        filing_date = raw.get("filing_date") or raw.get("filed_at")

        metrics = PortfolioMetrics(
            concentration_ratio_top10=sum(sorted([h.portfolio_ratio or 0.0 for h in holdings], reverse=True)[:10]),
            total_market_value=total_mv,
            positions_count=len(holdings),
        )
        return Portfolio(
            investor_slug=slug,
            report_date=datetime.fromisoformat(report_date) if isinstance(report_date, str) else None,
            filing_date=datetime.fromisoformat(filing_date) if isinstance(filing_date, str) else None,
            total_market_value=total_mv,
            stock_holdings=holdings,
            sector_breakdown=self._calc_sector_breakdown(holdings),
            portfolio_metrics=metrics,
        )

    @staticmethod
    def _calc_sector_breakdown(holdings: List[StockHolding]) -> Dict[str, float]:
        sector_sum: Dict[str, float] = {}
        total = 0.0
        for h in holdings:
            value = float(h.value or 0.0)
            total += value
            if h.sector:
                sector_sum[h.sector] = sector_sum.get(h.sector, 0.0) + value
        if total <= 0:
            return {}
        return {k: v / total for k, v in sector_sum.items()}

    def _save_profile(self, profile: InvestorProfile) -> None:
        col = self._col("investor_profiles")
        if col is None:
            return
        doc = to_dict(profile)
        col.update_one({"investor_info.slug": profile.investor_info.slug}, {"$set": doc}, upsert=True)

    def _col(self, name: str):
        return self.db.collection(name) if self.db else None




