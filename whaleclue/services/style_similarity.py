from __future__ import annotations

import math
from typing import Dict, List, Tuple

from whaleclue.models import InvestmentStyle


class StyleSimilarityService:
    """基于风格分数向量的相似度计算（余弦相似度）。"""

    STYLE_KEYS = ["Value", "Growth", "Momentum", "Quality", "Balanced"]

    @staticmethod
    def to_vector(style: InvestmentStyle) -> List[float]:
        return [float(style.style_scores.get(k, 0.0)) for k in StyleSimilarityService.STYLE_KEYS]

    @staticmethod
    def cosine_similarity(vec_a: List[float], vec_b: List[float]) -> float:
        dot = sum(a * b for a, b in zip(vec_a, vec_b))
        na = math.sqrt(sum(a * a for a in vec_a))
        nb = math.sqrt(sum(b * b for b in vec_b))
        if na == 0 or nb == 0:
            return 0.0
        return dot / (na * nb)

    def pairwise_similarity(self, styles: Dict[str, InvestmentStyle]) -> List[Tuple[str, str, float]]:
        items = list(styles.items())
        sims: List[Tuple[str, str, float]] = []
        for i in range(len(items)):
            slug_i, st_i = items[i]
            vi = self.to_vector(st_i)
            for j in range(i + 1, len(items)):
                slug_j, st_j = items[j]
                vj = self.to_vector(st_j)
                sims.append((slug_i, slug_j, self.cosine_similarity(vi, vj)))
        sims.sort(key=lambda x: x[2], reverse=True)
        return sims

    def top_similar(self, target_slug: str, styles: Dict[str, InvestmentStyle], top_k: int = 5) -> List[Tuple[str, float]]:
        if target_slug not in styles:
            return []
        target_vec = self.to_vector(styles[target_slug])
        results: List[Tuple[str, float]] = []
        for slug, st in styles.items():
            if slug == target_slug:
                continue
            sim = self.cosine_similarity(target_vec, self.to_vector(st))
            results.append((slug, sim))
        results.sort(key=lambda x: x[1], reverse=True)
        return results[:top_k]


