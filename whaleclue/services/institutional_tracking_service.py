from __future__ import annotations

import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional

from whaleclue.data.profit import ProfitDataClient
from whaleclue.services.portfolio_analysis_service import PortfolioAnalysisService


class InstitutionalTrackingService:
    """机构跟踪：本地watchlist持久化 + 最近交易时间线 + 最近两期持仓变化摘要。"""

    def __init__(self, profit_interface: ProfitDataClient, storage_dir: Optional[Path] = None):
        self.api = profit_interface
        self.pas = PortfolioAnalysisService()
        self.storage_dir = storage_dir or Path(__file__).resolve().parents[1]
        self.watchlist_path = self.storage_dir / "watchlist.json"

    # ---- watchlist 持久化 ----
    def load_watchlist(self) -> List[str]:
        try:
            if self.watchlist_path.exists():
                return list(set(json.loads(self.watchlist_path.read_text("utf-8")) or []))
        except Exception:
            return []
        return []

    def save_watchlist(self, slugs: List[str]) -> None:
        try:
            self.watchlist_path.write_text(json.dumps(sorted(list(set(slugs)))), encoding="utf-8")
        except Exception:
            pass

    # ---- 事件与变化 ----
    def get_recent_trades(self, slug: str, days_back: int = 30) -> List[Dict[str, Any]]:
        start = (datetime.utcnow() - timedelta(days=days_back)).date().isoformat()
        trades = self.api.get_investor_trades(slug=slug, start_date=start, end_date=datetime.utcnow().date().isoformat(), limit=300)
        if not isinstance(trades, list):
            return []
        trades.sort(key=lambda x: x.get("trade_date") or x.get("date") or "", reverse=True)
        return trades[:200]

    def get_latest_portfolio_changes(self, slug: str) -> Optional[Dict[str, Any]]:
        dates = self.api.get_investors_reporting_dates(investor_type="hedge_fund_manager", slug=slug)
        if isinstance(dates, dict):
            dates = dates.get("reporting_dates") or []
        if not dates or len(dates) < 2:
            return None
        dates_sorted = sorted(dates)
        cur_date = dates_sorted[-1]
        prev_date = dates_sorted[-2]
        cur_raw = self.api.get_investor_portfolio(slug=slug, report_date=cur_date)
        prev_raw = self.api.get_investor_portfolio(slug=slug, report_date=prev_date)
        # 轻量转化，仅用到权重
        def to_map(raw) -> Dict[str, float]:
            if not isinstance(raw, dict):
                return {}
            holdings = raw.get("stock_holdings") or raw.get("holdings") or raw.get("positions") or []
            m: Dict[str, float] = {}
            for h in holdings:
                t = h.get("ticker") or h.get("symbol")
                w = float(h.get("portfolio_ratio") or h.get("weight") or 0.0)
                if t:
                    m[t] = w
            return m
        cur = to_map(cur_raw)
        prev = to_map(prev_raw)
        # 差分
        new_positions = {t: w for t, w in cur.items() if t not in prev}
        closed = {t: w for t, w in prev.items() if t not in cur}
        increased: Dict[str, float] = {}
        decreased: Dict[str, float] = {}
        for t, w in cur.items():
            if t in prev:
                delta = w - prev[t]
                if delta > 0:
                    increased[t] = delta
                elif delta < 0:
                    decreased[t] = delta
        return {
            "current": cur_date,
            "previous": prev_date,
            "new_positions": dict(sorted(new_positions.items(), key=lambda x: x[1], reverse=True)[:20]),
            "increased": dict(sorted(increased.items(), key=lambda x: x[1], reverse=True)[:20]),
            "decreased": dict(sorted(decreased.items(), key=lambda x: x[1])[:20]),
            "closed": dict(sorted(closed.items(), key=lambda x: x[1], reverse=True)[:20]),
        }




