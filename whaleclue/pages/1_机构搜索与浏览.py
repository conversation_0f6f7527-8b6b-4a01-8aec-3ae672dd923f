import streamlit as st
from typing import List, Dict, Any

from shared import get_profit_interface, render_sidebar, render_investor_card


def main():
    st.set_page_config(page_title="机构搜索与浏览", layout="wide")
    iface = get_profit_interface()
    render_sidebar(st.session_state)

    col_q, col_cat, col_btn = st.columns([3, 2, 1])
    with col_q:
        query = st.text_input("搜索机构 (名称/slug/CIK)", value="")
    with col_cat:
        choice = st.radio("机构类别", ["全部", "Manager", "Insider"], horizontal=True)
    with col_btn:
        go = st.button("搜索", use_container_width=True)

    if choice == "Manager":
        investor_types = "hedge_fund_manager"
    elif choice == "Insider":
        investor_types = "corporate_insider"
    else:
        investor_types = ["hedge_fund_manager", "corporate_insider"]

    if go and (query or investor_types):
        with st.spinner("搜索中..."):
            try:
                resp = iface.search_investors(query=query, investor_type=investor_types, limit=50)
                results: List[Dict[str, Any]] = resp.get("data") if isinstance(resp, dict) else resp
                total = int(resp.get("total") or len(results) or 0) if isinstance(resp, dict) else len(results)
            except Exception as e:
                st.error(f"搜索失败: {e}")
                results = []
                total = 0
        if not results:
            st.info("未找到符合条件的机构")
        else:
            st.success(f"共找到 {total} 条结果")
            for item in results:
                with st.container(border=True):
                    render_investor_card(item)

    tracked = st.session_state.get("tracked_slugs", set())
    if tracked:
        st.subheader("已跟踪/对比的机构")
        st.write(", ".join(sorted(list(tracked))))


if __name__ == "__main__":
    main()



