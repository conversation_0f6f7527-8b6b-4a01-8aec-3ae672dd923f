import sys
from datetime import datetime
from typing import Dict, Any, List

import streamlit as st
from pathlib import Path

# sys.path 调整
APP_DIR = Path(__file__).resolve().parents[1]
PROJECT_ROOT = APP_DIR.parent
for p in [str(PROJECT_ROOT), str(APP_DIR)]:
    if p not in sys.path:
        sys.path.insert(0, p)

from whaleclue.data.profit import ProfitDataClient


@st.cache_resource(show_spinner=False)
def get_profit_interface() -> ProfitDataClient:
    return ProfitDataClient()


def _get_reporting_dates(slug: str) -> List[str]:
    iface = get_profit_interface()
    try:
        dates = iface.get_investors_reporting_dates(investor_type="hedge_fund_manager", slug=slug)
        if isinstance(dates, dict) and dates.get("reporting_dates"):
            return sorted(dates["reporting_dates"])  # type: ignore
        if isinstance(dates, list):
            return sorted(dates)
    except Exception:
        return []
    return []


def main():
    st.set_page_config(page_title="机构详情", layout="wide")
    slug = st.session_state.get("detail_slug")
    item: Dict[str, Any] = st.session_state.get("detail_item") or {}
    if not slug:
        st.info("缺少机构标识")
        st.stop()

    col1, col2 = st.columns([1, 3])
    with col1:
        if item.get("logo_url"):
            st.image(item["logo_url"], width=96)
    with col2:
        st.subheader(item.get("investor") or item.get("name") or item.get("company") or slug)
        st.caption(f"slug: {slug} | CIK: {item.get('cik', '-')}")
        if item.get("description"):
            st.write(item["description"])

    dates = _get_reporting_dates(slug)
    rdate = st.selectbox("报告期", options=dates[::-1] if dates else [""], index=0)
    iface = get_profit_interface()
    with st.spinner("加载持仓..."):
        port = iface.get_investor_portfolio(slug=slug, report_date=(rdate or None))
    if not isinstance(port, dict) or not port:
        st.info("暂无持仓数据")
        st.stop()

    items = port.get("stock_holdings") or port.get("holdings") or port.get("positions") or []
    top = sorted(items, key=lambda h: float(h.get("value") or h.get("market_value") or 0.0), reverse=True)[:20]
    st.subheader("Top 持仓")
    st.dataframe([
        {
            "Ticker": h.get("ticker") or h.get("symbol"),
            "名称": h.get("company_name") or h.get("name"),
            "市值": h.get("value") or h.get("market_value"),
            "权重%": h.get("portfolio_ratio") or h.get("weight"),
            "行业": h.get("sector"),
        } for h in top
    ], use_container_width=True)

    if st.button("返回", type="secondary"):
        st.session_state["route"] = "tabs"
        st.switch_page("streamlit_app.py")


if __name__ == "__main__":
    main()



