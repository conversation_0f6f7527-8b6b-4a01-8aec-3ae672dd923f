import sys
from typing import List, Dict, Any
from pathlib import Path

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Ensure both app directory and project root are on sys.path
APP_DIR = Path(__file__).resolve().parents[1]
PROJECT_ROOT = APP_DIR.parent
for p in [str(PROJECT_ROOT), str(APP_DIR)]:
    if p not in sys.path:
        sys.path.insert(0, p)

from shared import get_profit_interface, render_sidebar
from services.investment_style import InvestmentStyleService


def _get_reporting_dates(slug: str) -> List[str]:
    """Get reporting dates for a given slug"""
    iface = get_profit_interface()
    try:
        dates = iface.get_investors_reporting_dates(investor_type="hedge_fund_manager", slug=slug)
        if isinstance(dates, dict) and dates.get("reporting_dates"):
            return sorted(dates["reporting_dates"])
        if isinstance(dates, list):
            return sorted(dates)
    except Exception:
        return []
    return []


def main():
    st.set_page_config(page_title="风格对比", layout="wide")
    st.title("投资风格对比")
    
    tracked = st.session_state.get("tracked_slugs", set())
    if not tracked:
        st.info("请先在"机构搜索与浏览"页选择机构，并加入对比/跟踪")
        return

    if len(tracked) < 2:
        st.info("至少需要2个机构才能进行对比分析")
        return

    iface = get_profit_interface()
    style_service = InvestmentStyleService()

    # Institution selection
    st.subheader("选择对比机构")
    selected_institutions = st.multiselect(
        "选择要对比的机构（最多5个）",
        options=sorted(list(tracked)),
        default=sorted(list(tracked))[:min(5, len(tracked))]
    )

    if len(selected_institutions) < 2:
        st.warning("请至少选择2个机构进行对比")
        return

    # Date range selection
    st.subheader("分析期间")
    col1, col2 = st.columns(2)
    with col1:
        # Get common dates across all selected institutions
        common_dates = None
        for slug in selected_institutions:
            dates = _get_reporting_dates(slug)
            if common_dates is None:
                common_dates = set(dates)
            else:
                common_dates = common_dates.intersection(set(dates))
        
        if common_dates:
            common_dates = sorted(list(common_dates))
            date_range = st.slider("分析期间", 1, min(len(common_dates), 8), min(4, len(common_dates)))
            selected_dates = common_dates[-date_range:]
        else:
            st.warning("所选机构没有共同的报告期")
            return

    with col2:
        comparison_metrics = st.multiselect(
            "对比指标",
            ["集中度", "行业分散度", "市值偏好", "换手率", "风险指标"],
            default=["集中度", "行业分散度", "市值偏好"]
        )

    if st.button("开始对比分析", type="primary"):
        with st.spinner("分析各机构投资风格..."):
            institution_styles = {}
            
            for slug in selected_institutions:
                try:
                    portfolios = []
                    for date in selected_dates:
                        portfolio = iface.get_investor_portfolio(slug=slug, report_date=date)
                        if portfolio:
                            portfolios.append(portfolio)
                    
                    if portfolios:
                        style_analysis = style_service.analyze_investment_style(portfolios)
                        institution_styles[slug] = style_analysis
                    
                except Exception as e:
                    st.warning(f"分析 {slug} 失败: {e}")

        if not institution_styles:
            st.error("未能获取任何机构的风格分析数据")
            return

        # Display comparison results
        st.subheader("风格对比结果")

        # Metrics comparison table
        if "集中度" in comparison_metrics:
            st.subheader("集中度对比")
            concentration_data = []
            for slug, style in institution_styles.items():
                concentration = style.get("concentration", {})
                concentration_data.append({
                    "机构": slug,
                    "前5大持仓占比": f"{concentration.get('top_5_weight', 0):.1f}%",
                    "前10大持仓占比": f"{concentration.get('top_10_weight', 0):.1f}%",
                    "前20大持仓占比": f"{concentration.get('top_20_weight', 0):.1f}%"
                })
            st.dataframe(concentration_data, use_container_width=True)

        # Sector allocation comparison
        st.subheader("行业配置对比")
        sector_comparison = {}
        all_sectors = set()
        
        for slug, style in institution_styles.items():
            sector_allocation = style.get("sector_allocation", {})
            sector_comparison[slug] = sector_allocation
            all_sectors.update(sector_allocation.keys())

        if all_sectors:
            # Create sector comparison chart
            fig = go.Figure()
            
            for slug in selected_institutions:
                if slug in sector_comparison:
                    sectors = list(all_sectors)
                    weights = [sector_comparison[slug].get(sector, 0) for sector in sectors]
                    
                    fig.add_trace(go.Bar(
                        name=slug,
                        x=sectors,
                        y=weights,
                        text=[f"{w:.1f}%" for w in weights],
                        textposition='auto'
                    ))
            
            fig.update_layout(
                title="行业配置对比",
                xaxis_title="行业",
                yaxis_title="权重 (%)",
                barmode='group',
                height=500
            )
            st.plotly_chart(fig, use_container_width=True)

        # Risk-return scatter plot
        if "风险指标" in comparison_metrics:
            st.subheader("风险收益对比")
            
            risk_return_data = []
            for slug, style in institution_styles.items():
                risk_metrics = style.get("risk_metrics", {})
                risk_return_data.append({
                    "机构": slug,
                    "预期收益": risk_metrics.get("expected_return", 0) * 100,
                    "波动率": risk_metrics.get("volatility", 0) * 100,
                    "夏普比率": risk_metrics.get("sharpe_ratio", 0)
                })
            
            if risk_return_data:
                fig = px.scatter(
                    risk_return_data,
                    x="波动率",
                    y="预期收益",
                    color="夏普比率",
                    size="夏普比率",
                    hover_data=["机构"],
                    title="风险收益散点图"
                )
                fig.update_layout(
                    xaxis_title="波动率 (%)",
                    yaxis_title="预期收益 (%)"
                )
                st.plotly_chart(fig, use_container_width=True)

        # Market cap preference comparison
        if "市值偏好" in comparison_metrics:
            st.subheader("市值偏好对比")
            
            market_cap_data = []
            cap_categories = ["大盘股", "中盘股", "小盘股"]
            
            for slug, style in institution_styles.items():
                cap_dist = style.get("market_cap_distribution", {})
                market_cap_data.append({
                    "机构": slug,
                    **{cat: cap_dist.get(cat, 0) for cat in cap_categories}
                })
            
            if market_cap_data:
                # Create stacked bar chart
                fig = go.Figure()
                
                for cat in cap_categories:
                    values = [data[cat] for data in market_cap_data]
                    institutions = [data["机构"] for data in market_cap_data]
                    
                    fig.add_trace(go.Bar(
                        name=cat,
                        x=institutions,
                        y=values,
                        text=[f"{v:.1f}%" for v in values],
                        textposition='auto'
                    ))
                
                fig.update_layout(
                    title="市值偏好分布",
                    xaxis_title="机构",
                    yaxis_title="权重 (%)",
                    barmode='stack',
                    height=400
                )
                st.plotly_chart(fig, use_container_width=True)

        # Style similarity analysis
        st.subheader("风格相似度分析")
        
        if len(selected_institutions) >= 2:
            similarity_matrix = style_service.calculate_style_similarity(
                [institution_styles[slug] for slug in selected_institutions if slug in institution_styles]
            )
            
            if similarity_matrix is not None:
                fig = px.imshow(
                    similarity_matrix,
                    x=selected_institutions,
                    y=selected_institutions,
                    color_continuous_scale="RdYlBu_r",
                    title="风格相似度矩阵"
                )
                fig.update_layout(height=400)
                st.plotly_chart(fig, use_container_width=True)
                
                st.caption("相似度越高（红色），表示两个机构的投资风格越相近")

        # Summary insights
        st.subheader("对比洞察")
        
        insights = style_service.generate_comparison_insights(institution_styles)
        if insights:
            for insight in insights:
                st.info(f"💡 {insight}")


if __name__ == "__main__":
    main()
