import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

import streamlit as st

# Ensure both app directory and project root are on sys.path
APP_DIR = Path(__file__).resolve().parents[1]
PROJECT_ROOT = APP_DIR.parent
for p in [str(PROJECT_ROOT), str(APP_DIR)]:
    if p not in sys.path:
        sys.path.insert(0, p)

from shared import get_profit_interface, render_sidebar
from services.user_settings import UserSettingsService
from services.investment_clue import InvestmentClueService
from ai.agent import get_insights_with_agent


def _get_latest_report_date_for_slug(slug: str) -> Optional[str]:
    """Get the latest report date for a given slug"""
    iface = get_profit_interface()
    try:
        dates = iface.get_investors_reporting_dates(investor_type="hedge_fund_manager", slug=slug)
        if isinstance(dates, dict) and dates.get("reporting_dates"):
            return sorted(dates["reporting_dates"])[-1]
        if isinstance(dates, list) and dates:
            return sorted(dates)[-1]
    except Exception:
        pass
    return None


def _collect_portfolios(slugs: List[str], report_date: Optional[str]) -> List[Dict[str, Any]]:
    """Collect portfolios for given slugs and report date"""
    iface = get_profit_interface()
    portfolios = []
    for slug in slugs:
        try:
            port = iface.get_investor_portfolio(slug=slug, report_date=report_date)
            if port:
                portfolios.append(port)
        except Exception as e:
            st.warning(f"获取 {slug} 持仓失败: {e}")
    return portfolios


def _unusual_trades(slugs: List[str], start_date: datetime) -> List[Dict[str, Any]]:
    """Get unusual trades for given slugs since start_date"""
    # This would need to be implemented based on your data interface
    # For now, return empty list
    return []


def main():
    st.set_page_config(page_title="投资线索发现", layout="wide")
    st.title("投资线索发现")
    
    tracked = st.session_state.get("tracked_slugs", set())
    if not tracked:
        st.info("请先在"机构搜索与浏览"页选择机构，并加入对比/跟踪")
        return

    st.write("本页聚焦共识股票与异常交易，帮助发现线索")

    usvc = UserSettingsService()
    u = usvc.load()
    min_inst = st.slider("共识最少机构数", 1, max(2, len(tracked)), int(u.get("min_consensus_institutions", 2)))
    days = int(u.get("anomaly_window_days", st.session_state.get("trade_days", 90)))
    left, right = st.columns(2)
    with left:
        report_date_opt = st.text_input("报告期(YYYY-MM-DD，可留空取最近)", value="")
    with right:
        start_date = datetime.utcnow() - timedelta(days=days)
        st.caption(f"交易窗口: 自 {start_date.date()} 至今")

    report_date = report_date_opt.strip() or None
    if report_date is None:
        # Take the first slug's latest report date as reference
        any_slug = sorted(list(tracked))[0]
        report_date = _get_latest_report_date_for_slug(any_slug)

    st.write(f"使用报告期: {report_date or '最新可用'}")

    with st.spinner("获取组合并计算共识..."):
        portfolios = _collect_portfolios(sorted(list(tracked)), report_date)
        clue_srv = InvestmentClueService()
        picks = clue_srv.find_consensus_picks(portfolios, min_institutions=min_inst)

    if picks:
        st.subheader("共识股票")
        for p in picks[:20]:
            with st.container(border=True):
                st.write(f"{p['ticker']} | 机构数: {p['institution_count']} | 平均权重: {p['avg_weight']:.2f}%")
                st.caption("机构: " + ", ".join(p.get("institutions", [])))
    else:
        st.info("未发现符合条件的共识股票")

    with st.spinner("检测异常交易..."):
        anomalies = _unusual_trades(sorted(list(tracked)), start_date)
        anomalies = clue_srv.detect_unusual_trades(anomalies)

    if anomalies:
        st.subheader("异常交易")
        for a in anomalies[:20]:
            with st.container(border=True):
                st.write(f"{a['ticker']} | 机构: {a['institution']} | 异常规模: {a['anomaly_size']:.0f}")
                st.caption(f"交易日期: {a.get('trade_date', 'N/A')}")

    smart_moves = InvestmentClueService().detect_smart_money_moves(anomalies=anomalies, consensus=picks)
    if smart_moves:
        st.subheader("聪明钱动向（简版）")
        for m in smart_moves[:20]:
            with st.container(border=True):
                st.write(
                    f"{m['ticker']} | 机构数: {m['institution_count']} | 异常规模: {m['anomaly_size']:.0f} | 平均权重: {m['avg_weight']:.2f}%")
                st.caption("机构: " + ", ".join(m.get("institutions", [])))

    if st.button("使用 AI 生成洞见与建议", type="primary"):
        with st.spinner("AI 正在分析..."):
            insights = get_insights_with_agent(picks=picks, anomalies=anomalies)
        st.markdown(insights)


if __name__ == "__main__":
    main()
