import sys
from typing import List, Dict, Any
from pathlib import Path

import streamlit as st
import plotly.express as px

# Ensure both app directory and project root are on sys.path
APP_DIR = Path(__file__).resolve().parents[1]
PROJECT_ROOT = APP_DIR.parent
for p in [str(PROJECT_ROOT), str(APP_DIR)]:
    if p not in sys.path:
        sys.path.insert(0, p)

from shared import get_profit_interface, render_sidebar
from services.investment_style import InvestmentStyleService


def _get_reporting_dates(slug: str) -> List[str]:
    """Get reporting dates for a given slug"""
    iface = get_profit_interface()
    try:
        dates = iface.get_investors_reporting_dates(investor_type="hedge_fund_manager", slug=slug)
        if isinstance(dates, dict) and dates.get("reporting_dates"):
            return sorted(dates["reporting_dates"])
        if isinstance(dates, list):
            return sorted(dates)
    except Exception:
        return []
    return []


def main():
    st.set_page_config(page_title="投资风格分析", layout="wide")
    st.title("投资风格分析")
    
    tracked = st.session_state.get("tracked_slugs", set())
    if not tracked:
        st.info("请先在"机构搜索与浏览"页选择机构，并加入对比/跟踪")
        return

    iface = get_profit_interface()
    style_service = InvestmentStyleService()

    slug = st.selectbox("选择机构", sorted(list(tracked)))
    dates = _get_reporting_dates(slug)
    
    if not dates:
        st.warning("未获取到报告期数据")
        return
        
    date_range = st.slider("分析期间", 1, min(len(dates), 8), min(4, len(dates)))
    selected_dates = dates[-date_range:]

    with st.spinner("分析投资风格..."):
        try:
            portfolios = []
            for date in selected_dates:
                portfolio = iface.get_investor_portfolio(slug=slug, report_date=date)
                if portfolio:
                    portfolios.append(portfolio)
            
            if not portfolios:
                st.warning("未获取到有效的持仓数据")
                return
                
            style_analysis = style_service.analyze_investment_style(portfolios)
            
        except Exception as e:
            st.error(f"分析失败: {e}")
            return

    if not style_analysis:
        st.warning("风格分析结果为空")
        return

    # Display style metrics
    st.subheader("投资风格概览")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        concentration = style_analysis.get("concentration", {})
        st.metric("集中度", f"{concentration.get('top_10_weight', 0):.1f}%", 
                 help="前10大持仓占比")
    
    with col2:
        sector_div = style_analysis.get("sector_diversification", 0)
        st.metric("行业分散度", f"{sector_div:.2f}", 
                 help="行业分散程度，越高越分散")
    
    with col3:
        avg_market_cap = style_analysis.get("avg_market_cap", 0)
        if avg_market_cap > 1e9:
            cap_display = f"{avg_market_cap/1e9:.1f}B"
        elif avg_market_cap > 1e6:
            cap_display = f"{avg_market_cap/1e6:.1f}M"
        else:
            cap_display = f"{avg_market_cap:.0f}"
        st.metric("平均市值", cap_display)
    
    with col4:
        turnover = style_analysis.get("portfolio_turnover", 0)
        st.metric("换手率", f"{turnover:.1f}%", 
                 help="持仓变化频率")

    # Sector allocation
    sector_allocation = style_analysis.get("sector_allocation", {})
    if sector_allocation:
        st.subheader("行业配置")
        fig = px.bar(x=list(sector_allocation.keys()), 
                    y=list(sector_allocation.values()),
                    title="行业权重分布")
        fig.update_layout(xaxis_title="行业", yaxis_title="权重 (%)")
        st.plotly_chart(fig, use_container_width=True)

    # Market cap distribution
    market_cap_dist = style_analysis.get("market_cap_distribution", {})
    if market_cap_dist:
        st.subheader("市值分布")
        fig = px.pie(values=list(market_cap_dist.values()), 
                    names=list(market_cap_dist.keys()),
                    title="市值分布")
        st.plotly_chart(fig, use_container_width=True)

    # Style classification
    style_class = style_analysis.get("style_classification", {})
    if style_class:
        st.subheader("风格分类")
        
        col1, col2 = st.columns(2)
        with col1:
            st.write("**投资风格特征:**")
            for key, value in style_class.items():
                if isinstance(value, (int, float)):
                    st.write(f"• {key}: {value:.2f}")
                else:
                    st.write(f"• {key}: {value}")
        
        with col2:
            # Risk metrics
            risk_metrics = style_analysis.get("risk_metrics", {})
            if risk_metrics:
                st.write("**风险指标:**")
                for key, value in risk_metrics.items():
                    if isinstance(value, (int, float)):
                        st.write(f"• {key}: {value:.3f}")
                    else:
                        st.write(f"• {key}: {value}")

    # Historical trend
    historical_data = style_analysis.get("historical_trends", {})
    if historical_data and len(selected_dates) > 1:
        st.subheader("历史趋势")
        
        # Create trend charts for key metrics
        if "concentration_trend" in historical_data:
            fig = px.line(x=selected_dates, 
                         y=historical_data["concentration_trend"],
                         title="集中度变化趋势")
            fig.update_layout(xaxis_title="报告期", yaxis_title="前10大持仓占比 (%)")
            st.plotly_chart(fig, use_container_width=True)


if __name__ == "__main__":
    main()
