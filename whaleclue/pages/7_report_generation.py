import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

import streamlit as st

# Ensure both app directory and project root are on sys.path
APP_DIR = Path(__file__).resolve().parents[1]
PROJECT_ROOT = APP_DIR.parent
for p in [str(PROJECT_ROOT), str(APP_DIR)]:
    if p not in sys.path:
        sys.path.insert(0, p)

from shared import get_profit_interface, render_sidebar
from services.user_settings import UserSettingsService
from services.investment_clue import InvestmentClueService
from services.investment_recommendation import InvestmentRecommendationService
from services.report import ReportService


def _get_latest_report_date_for_slug(slug: str) -> Optional[str]:
    """Get the latest report date for a given slug"""
    iface = get_profit_interface()
    try:
        dates = iface.get_investors_reporting_dates(investor_type="hedge_fund_manager", slug=slug)
        if isinstance(dates, dict) and dates.get("reporting_dates"):
            return sorted(dates["reporting_dates"])[-1]
        if isinstance(dates, list) and dates:
            return sorted(dates)[-1]
    except Exception:
        pass
    return None


def _collect_portfolios(slugs: List[str], report_date: Optional[str]) -> List[Dict[str, Any]]:
    """Collect portfolios for given slugs and report date"""
    iface = get_profit_interface()
    portfolios = []
    for slug in slugs:
        try:
            port = iface.get_investor_portfolio(slug=slug, report_date=report_date)
            if port:
                portfolios.append(port)
        except Exception as e:
            st.warning(f"获取 {slug} 持仓失败: {e}")
    return portfolios


def _unusual_trades(slugs: List[str], start_date: datetime) -> List[Dict[str, Any]]:
    """Get unusual trades for given slugs since start_date"""
    # This would need to be implemented based on your data interface
    # For now, return empty list
    return []


def main():
    st.set_page_config(page_title="报告生成", layout="wide")
    st.title("报告生成")
    
    tracked = st.session_state.get("tracked_slugs", set())
    if not tracked:
        st.info("请先在"机构搜索与浏览"页选择机构，并加入对比/跟踪")
        return

    slugs = sorted(list(tracked))
    usvc = UserSettingsService()
    u = usvc.load()

    # Report configuration
    st.subheader("报告配置")
    
    col1, col2 = st.columns(2)
    with col1:
        title = st.text_input("报告标题", 
                            value=f"机构投资分析报告 - {datetime.utcnow().strftime('%Y-%m-%d')}")
        report_type = st.selectbox("报告类型", 
                                 ["综合分析报告", "投资线索报告", "持仓分析报告", "风格分析报告"])
    
    with col2:
        min_inst = st.slider("共识最少机构数", 1, max(2, len(slugs)), 
                           int(u.get("min_consensus_institutions", 2)))
        days = st.slider("异常交易窗口(天)", 7, 365, 
                        int(u.get("anomaly_window_days", 90)))

    report_date_opt = st.text_input("报告期(YYYY-MM-DD，可留空取最近)", value="")
    
    # Report sections selection
    st.subheader("报告内容")
    sections = st.multiselect(
        "选择报告章节",
        ["执行摘要", "机构概览", "共识股票分析", "异常交易分析", "投资建议", "风险提示", "附录"],
        default=["执行摘要", "机构概览", "共识股票分析", "投资建议"]
    )

    # Additional options
    with st.expander("高级选项"):
        include_charts = st.checkbox("包含图表", value=True)
        include_raw_data = st.checkbox("包含原始数据", value=False)
        language = st.selectbox("报告语言", ["中文", "English"], index=0)

    if st.button("生成报告", type="primary"):
        with st.spinner("收集数据..."):
            report_date = report_date_opt.strip() or None
            if report_date is None:
                report_date = _get_latest_report_date_for_slug(slugs[0])
            
            # Collect all necessary data
            portfolios = _collect_portfolios(slugs, report_date)
            clue_srv = InvestmentClueService()
            consensus = clue_srv.find_consensus_picks(portfolios, min_institutions=min_inst)
            anomalies = _unusual_trades(slugs, datetime.utcnow() - timedelta(days=days))
            anomalies = clue_srv.detect_unusual_trades(anomalies)
            
            rec_srv = InvestmentRecommendationService()
            recommendations = rec_srv.generate_recommendations(
                consensus=consensus, anomalies=anomalies, style=None, top_k=10
            )

        with st.spinner("生成报告..."):
            report_srv = ReportService()
            
            # Generate different report types
            if report_type == "综合分析报告":
                html = report_srv.build_comprehensive_report(
                    title=title,
                    institutions=slugs,
                    consensus=consensus,
                    anomalies=anomalies,
                    recommendations=recommendations,
                    sections=sections,
                    include_charts=include_charts,
                    language=language
                )
            elif report_type == "投资线索报告":
                html = report_srv.build_clues_report(
                    title=title,
                    consensus=consensus,
                    anomalies=anomalies,
                    include_charts=include_charts
                )
            elif report_type == "持仓分析报告":
                html = report_srv.build_portfolio_report(
                    title=title,
                    portfolios=portfolios,
                    include_charts=include_charts
                )
            else:  # 风格分析报告
                html = report_srv.build_style_report(
                    title=title,
                    institutions=slugs,
                    include_charts=include_charts
                )

        # Display report preview
        st.subheader("报告预览")
        with st.expander("查看HTML内容", expanded=False):
            st.code(html[:2000] + "..." if len(html) > 2000 else html, language="html")

        # Download options
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.download_button(
                label="下载HTML报告",
                data=html,
                file_name=f"report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.html",
                mime="text/html",
            )
        
        with col2:
            # Convert to PDF (if needed)
            if st.button("生成PDF版本"):
                with st.spinner("转换为PDF..."):
                    try:
                        pdf_data = report_srv.html_to_pdf(html)
                        st.download_button(
                            label="下载PDF报告",
                            data=pdf_data,
                            file_name=f"report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.pdf",
                            mime="application/pdf",
                        )
                    except Exception as e:
                        st.error(f"PDF生成失败: {e}")
        
        with col3:
            # Email report (if configured)
            if st.button("发送邮件"):
                email = st.text_input("收件人邮箱")
                if email:
                    try:
                        report_srv.send_email_report(html, email, title)
                        st.success("报告已发送")
                    except Exception as e:
                        st.error(f"发送失败: {e}")

    # Report history
    st.subheader("历史报告")
    if st.button("查看历史报告"):
        try:
            history = report_srv.get_report_history()
            if history:
                for report in history[-10:]:  # Show last 10 reports
                    with st.container(border=True):
                        col1, col2, col3 = st.columns([3, 2, 1])
                        with col1:
                            st.write(f"**{report.get('title', 'Untitled')}**")
                            st.caption(f"生成时间: {report.get('created_at', 'N/A')}")
                        with col2:
                            st.write(f"类型: {report.get('type', 'N/A')}")
                            st.write(f"大小: {report.get('size', 'N/A')}")
                        with col3:
                            if st.button("下载", key=f"download_{report.get('id')}"):
                                # Download historical report
                                pass
            else:
                st.info("暂无历史报告")
        except Exception as e:
            st.error(f"加载历史报告失败: {e}")


if __name__ == "__main__":
    main()
