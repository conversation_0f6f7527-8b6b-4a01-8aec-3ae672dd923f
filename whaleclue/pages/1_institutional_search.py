import sys
from typing import List, Dict, Any
from pathlib import Path

import streamlit as st

# Ensure both app directory and project root are on sys.path
APP_DIR = Path(__file__).resolve().parents[1]
PROJECT_ROOT = APP_DIR.parent
for p in [str(PROJECT_ROOT), str(APP_DIR)]:
    if p not in sys.path:
        sys.path.insert(0, p)

from shared import get_profit_interface, render_sidebar, render_investor_card
from services.user_settings import UserSettingsService


def main():
    st.set_page_config(page_title="机构搜索与浏览", layout="wide")
    st.title("机构搜索与浏览")
    
    iface = get_profit_interface()
    render_sidebar(st.session_state)

    # Top search input and category switching (not in sidebar)
    u = UserSettingsService().load()
    col_q, col_cat, col_btn = st.columns([3, 2, 1])
    with col_q:
        query = st.text_input("搜索机构 (名称/slug/CIK)", value="")
    with col_cat:
        choice = st.radio("机构类别", ["全部", "Manager", "Insider"], horizontal=True)
    with col_btn:
        go = st.button("搜索", use_container_width=True)

    if choice == "Manager":
        investor_types = "hedge_fund_manager"
    elif choice == "Insider":
        investor_types = "corporate_insider"
    else:
        investor_types = ["hedge_fund_manager", "corporate_insider"]

    if go and (query or investor_types):
        with st.spinner("搜索中..."):
            try:
                results: List[Dict[str, Any]] = iface.search_investors(query=query, investor_type=investor_types,
                                                                         limit=50)
            except Exception as e:
                st.error(f"搜索失败: {e}")
                results = []
        if not results:
            st.info("未找到符合条件的机构")
            return
        st.success(f"共找到 {len(results)} 条结果")
        for item in results:
            with st.container(border=True):
                render_investor_card(item)

    tracked = st.session_state.get("tracked_slugs", set())
    if tracked:
        st.subheader("已跟踪/对比的机构")
        st.write(", ".join(sorted(list(tracked))))


if __name__ == "__main__":
    main()
