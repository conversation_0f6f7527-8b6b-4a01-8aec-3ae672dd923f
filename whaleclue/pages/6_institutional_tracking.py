import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any
from pathlib import Path

import streamlit as st
import plotly.express as px

# Ensure both app directory and project root are on sys.path
APP_DIR = Path(__file__).resolve().parents[1]
PROJECT_ROOT = APP_DIR.parent
for p in [str(PROJECT_ROOT), str(APP_DIR)]:
    if p not in sys.path:
        sys.path.insert(0, p)

from shared import get_profit_interface, render_sidebar
from services.institutional_tracking import InstitutionalTrackingService


def main():
    st.set_page_config(page_title="机构跟踪", layout="wide")
    st.title("机构跟踪")
    
    iface = get_profit_interface()
    tsvc = InstitutionalTrackingService(iface)
    current_list = set(tsvc.load_watchlist())
    tracked = set(st.session_state.get("tracked_slugs", set()))
    merged = sorted(list(current_list.union(tracked)))
    
    # Watchlist management
    st.subheader("跟踪列表管理")
    sel = st.multiselect("跟踪机构列表", options=merged, default=merged)
    if st.button("保存列表"):
        tsvc.save_watchlist(sel)
        st.success("已保存")

    if not sel:
        st.info("添加一些机构到跟踪列表以查看时间线和持仓变化")
        return

    # Timeline and portfolio changes
    st.subheader("机构动态时间线")
    
    # Date range selection
    col1, col2 = st.columns(2)
    with col1:
        start_date = st.date_input("开始日期", 
                                 value=datetime.now().date() - timedelta(days=90))
    with col2:
        end_date = st.date_input("结束日期", 
                               value=datetime.now().date())

    if st.button("加载时间线", type="primary"):
        with st.spinner("加载机构动态..."):
            try:
                timeline = tsvc.get_timeline(sel, start_date.strftime('%Y-%m-%d'), 
                                           end_date.strftime('%Y-%m-%d'))
                
                if timeline:
                    st.subheader("重要事件时间线")
                    for event in timeline[:50]:  # Show latest 50 events
                        with st.container(border=True):
                            col1, col2, col3 = st.columns([2, 3, 1])
                            with col1:
                                st.write(f"**{event.get('date', 'N/A')}**")
                                st.caption(event.get('institution', ''))
                            with col2:
                                st.write(event.get('event_type', ''))
                                if event.get('description'):
                                    st.caption(event['description'])
                            with col3:
                                if event.get('impact_score'):
                                    st.metric("影响", f"{event['impact_score']:.1f}")
                else:
                    st.info("未找到相关事件")
                    
            except Exception as e:
                st.error(f"加载时间线失败: {e}")

    # Portfolio changes summary
    st.subheader("持仓变化汇总")
    
    if len(sel) > 0:
        selected_institution = st.selectbox("选择机构查看详细变化", sel)
        
        if st.button("分析持仓变化"):
            with st.spinner("分析持仓变化..."):
                try:
                    changes = tsvc.analyze_portfolio_changes(
                        selected_institution, 
                        start_date.strftime('%Y-%m-%d'),
                        end_date.strftime('%Y-%m-%d')
                    )
                    
                    if changes:
                        col1, col2, col3 = st.columns(3)
                        
                        with col1:
                            st.metric("新增持仓", len(changes.get("new_positions", [])))
                        with col2:
                            st.metric("增持", len(changes.get("increased_positions", [])))
                        with col3:
                            st.metric("减持", len(changes.get("reduced_positions", [])))
                        
                        # Show top changes
                        if changes.get("top_changes"):
                            st.subheader("主要持仓变化")
                            for change in changes["top_changes"][:10]:
                                with st.container(border=True):
                                    col1, col2 = st.columns([3, 1])
                                    with col1:
                                        st.write(f"**{change.get('ticker')}** - {change.get('company_name', '')}")
                                        st.caption(f"变化类型: {change.get('change_type', '')}")
                                    with col2:
                                        change_pct = change.get('change_percentage', 0)
                                        color = "normal" if change_pct >= 0 else "inverse"
                                        st.metric("变化", f"{change_pct:+.1f}%", 
                                                delta_color=color)
                    else:
                        st.info("未发现显著的持仓变化")
                        
                except Exception as e:
                    st.error(f"分析失败: {e}")

    # Performance tracking
    st.subheader("跟踪机构表现")
    
    if st.button("加载表现数据"):
        with st.spinner("计算表现指标..."):
            try:
                performance_data = []
                for institution in sel[:5]:  # Limit to 5 institutions for performance
                    perf = tsvc.get_performance_metrics(
                        institution,
                        start_date.strftime('%Y-%m-%d'),
                        end_date.strftime('%Y-%m-%d')
                    )
                    if perf:
                        performance_data.append({
                            "机构": institution,
                            "收益率": f"{perf.get('return', 0):.2f}%",
                            "夏普比率": f"{perf.get('sharpe_ratio', 0):.2f}",
                            "最大回撤": f"{perf.get('max_drawdown', 0):.2f}%",
                            "胜率": f"{perf.get('win_rate', 0):.1f}%"
                        })
                
                if performance_data:
                    st.dataframe(performance_data, use_container_width=True)
                else:
                    st.info("暂无表现数据")
                    
            except Exception as e:
                st.error(f"加载表现数据失败: {e}")


if __name__ == "__main__":
    main()
