import sys
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

import streamlit as st
import plotly.express as px

# Ensure both app directory and project root are on sys.path
APP_DIR = Path(__file__).resolve().parents[1]
PROJECT_ROOT = APP_DIR.parent
for p in [str(PROJECT_ROOT), str(APP_DIR)]:
    if p not in sys.path:
        sys.path.insert(0, p)

from shared import get_profit_interface, render_sidebar
from services.portfolio_analysis import PortfolioAnalysisService


def _get_reporting_dates(slug: str) -> List[str]:
    """Get reporting dates for a given slug"""
    iface = get_profit_interface()
    try:
        dates = iface.get_investors_reporting_dates(investor_type="hedge_fund_manager", slug=slug)
        if isinstance(dates, dict) and dates.get("reporting_dates"):
            return sorted(dates["reporting_dates"])
        if isinstance(dates, list):
            return sorted(dates)
    except Exception:
        return []
    return []


def main():
    st.set_page_config(page_title="持仓分析", layout="wide")
    st.title("持仓分析")
    
    tracked = st.session_state.get("tracked_slugs", set())
    if not tracked:
        st.info("请先在"机构搜索与浏览"页选择机构，并加入对比/跟踪")
        return
    
    iface = get_profit_interface()
    pas = PortfolioAnalysisService()

    slug = st.selectbox("选择机构", sorted(list(tracked)))
    dates = _get_reporting_dates(slug)
    if not dates:
        st.warning("未获取到报告期，尝试使用最近一个")
    cur_date = st.selectbox("当前报告期", options=dates[::-1] if dates else [""], index=0)
    prev_date = st.selectbox("对比报告期", options=dates[::-1][1:] if dates and len(dates) > 1 else [""], index=0)

    if not cur_date:
        st.info("请选择报告期")
        return

    with st.spinner("加载持仓数据..."):
        try:
            current_portfolio = iface.get_investor_portfolio(slug=slug, report_date=cur_date)
            if prev_date:
                previous_portfolio = iface.get_investor_portfolio(slug=slug, report_date=prev_date)
            else:
                previous_portfolio = None
        except Exception as e:
            st.error(f"加载数据失败: {e}")
            return

    if not current_portfolio:
        st.warning("当前报告期无持仓数据")
        return

    # Display current portfolio
    st.subheader(f"当前持仓 ({cur_date})")
    current_holdings = current_portfolio.get("stock_holdings") or current_portfolio.get("holdings") or []
    
    if current_holdings:
        # Top holdings
        top_holdings = sorted(current_holdings, 
                            key=lambda h: float(h.get("value") or h.get("market_value") or 0), 
                            reverse=True)[:20]
        
        holdings_df = []
        for h in top_holdings:
            holdings_df.append({
                "Ticker": h.get("ticker") or h.get("symbol"),
                "公司名称": h.get("company_name") or h.get("name"),
                "市值": h.get("value") or h.get("market_value"),
                "权重%": h.get("portfolio_ratio") or h.get("weight"),
                "股数": h.get("shares"),
                "行业": h.get("sector"),
            })
        
        st.dataframe(holdings_df, use_container_width=True)
        
        # Portfolio composition by sector
        if any(h.get("sector") for h in current_holdings):
            sector_data = {}
            for h in current_holdings:
                sector = h.get("sector") or "其他"
                value = float(h.get("value") or h.get("market_value") or 0)
                sector_data[sector] = sector_data.get(sector, 0) + value
            
            if sector_data:
                fig = px.pie(values=list(sector_data.values()), 
                           names=list(sector_data.keys()), 
                           title="行业分布")
                st.plotly_chart(fig, use_container_width=True)

    # Portfolio comparison if previous data available
    if previous_portfolio and prev_date:
        st.subheader(f"持仓变化 ({prev_date} → {cur_date})")
        
        with st.spinner("分析持仓变化..."):
            changes = pas.analyze_portfolio_changes(previous_portfolio, current_portfolio)
        
        if changes:
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("新增持仓", len(changes.get("new_positions", [])))
            with col2:
                st.metric("减持/清仓", len(changes.get("reduced_positions", [])))
            with col3:
                st.metric("增持", len(changes.get("increased_positions", [])))
            
            # Show significant changes
            if changes.get("new_positions"):
                st.subheader("新增持仓")
                for pos in changes["new_positions"][:10]:
                    st.write(f"• {pos.get('ticker')} - {pos.get('company_name')}")
            
            if changes.get("reduced_positions"):
                st.subheader("减持/清仓")
                for pos in changes["reduced_positions"][:10]:
                    st.write(f"• {pos.get('ticker')} - {pos.get('company_name')}")


if __name__ == "__main__":
    main()
