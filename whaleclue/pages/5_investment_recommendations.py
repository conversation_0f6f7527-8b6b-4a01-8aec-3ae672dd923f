import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

import streamlit as st

# Ensure both app directory and project root are on sys.path
APP_DIR = Path(__file__).resolve().parents[1]
PROJECT_ROOT = APP_DIR.parent
for p in [str(PROJECT_ROOT), str(APP_DIR)]:
    if p not in sys.path:
        sys.path.insert(0, p)

from shared import get_profit_interface, render_sidebar
from services.user_settings import UserSettingsService
from services.investment_clue import InvestmentClueService
from services.investment_recommendation import InvestmentRecommendationService
from services.report import ReportService


def _get_latest_report_date_for_slug(slug: str) -> Optional[str]:
    """Get the latest report date for a given slug"""
    iface = get_profit_interface()
    try:
        dates = iface.get_investors_reporting_dates(investor_type="hedge_fund_manager", slug=slug)
        if isinstance(dates, dict) and dates.get("reporting_dates"):
            return sorted(dates["reporting_dates"])[-1]
        if isinstance(dates, list) and dates:
            return sorted(dates)[-1]
    except Exception:
        pass
    return None


def _collect_portfolios(slugs: List[str], report_date: Optional[str]) -> List[Dict[str, Any]]:
    """Collect portfolios for given slugs and report date"""
    iface = get_profit_interface()
    portfolios = []
    for slug in slugs:
        try:
            port = iface.get_investor_portfolio(slug=slug, report_date=report_date)
            if port:
                portfolios.append(port)
        except Exception as e:
            st.warning(f"获取 {slug} 持仓失败: {e}")
    return portfolios


def _unusual_trades(slugs: List[str], start_date: datetime) -> List[Dict[str, Any]]:
    """Get unusual trades for given slugs since start_date"""
    # This would need to be implemented based on your data interface
    # For now, return empty list
    return []


def main():
    st.set_page_config(page_title="投资建议", layout="wide")
    st.title("投资建议")
    
    tracked = st.session_state.get("tracked_slugs", set())
    if not tracked:
        st.info("请先在"机构搜索与浏览"页选择机构，并加入对比/跟踪")
        return

    iface = get_profit_interface()
    slugs = sorted(list(tracked))
    usvc = UserSettingsService()
    u = usvc.load()
    
    # Configuration section
    st.subheader("配置参数")
    col1, col2 = st.columns(2)
    
    with col1:
        min_inst = st.slider("共识最少机构数", 1, max(2, len(slugs)), 
                           int(u.get("min_consensus_institutions", 2)),
                           key="rec_min_inst")
        report_date_opt = st.text_input("报告期(YYYY-MM-DD，可留空取最近)", 
                                      value="", key="rec_report")
    
    with col2:
        days = st.slider("异常交易窗口(天)", 7, 365, 
                        int(u.get("anomaly_window_days", 90)), key="rec_days")
        risk = st.selectbox("风险偏好", ["conservative", "moderate", "aggressive"],
                          index=["conservative", "moderate", "aggressive"].index(
                              u.get("risk_profile", "moderate")))

    if st.button("生成建议", type="primary"):
        with st.spinner("汇总线索..."):
            report_date = report_date_opt.strip() or None
            if report_date is None:
                report_date = _get_latest_report_date_for_slug(slugs[0])
            portfolios = _collect_portfolios(slugs, report_date)
            clue_srv = InvestmentClueService()
            consensus = clue_srv.find_consensus_picks(portfolios, min_institutions=min_inst)
            anomalies = _unusual_trades(slugs, datetime.utcnow() - timedelta(days=days))
            anomalies = clue_srv.detect_unusual_trades(anomalies)

        with st.spinner("生成建议..."):
            rec_srv = InvestmentRecommendationService()
            recs = rec_srv.generate_recommendations(consensus=consensus, anomalies=anomalies, 
                                                  style=None, top_k=10, risk_profile=risk)

        if recs:
            st.subheader("投资建议")
            for i, rec in enumerate(recs, 1):
                with st.container(border=True):
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        st.write(f"**{i}. {rec.get('ticker')} - {rec.get('company_name', '')}**")
                        st.write(f"建议: {rec.get('recommendation', '')}")
                        if rec.get('reasoning'):
                            st.caption(f"理由: {rec['reasoning']}")
                    with col2:
                        confidence = rec.get('confidence', 0)
                        st.metric("置信度", f"{confidence:.1f}%")
                        
                        risk_level = rec.get('risk_level', 'medium')
                        risk_color = {"low": "🟢", "medium": "🟡", "high": "🔴"}.get(risk_level, "🟡")
                        st.write(f"风险: {risk_color} {risk_level}")
        else:
            st.info("未生成投资建议")

    # Report generation section
    st.subheader("报告生成")
    title = st.text_input("报告标题", value=f"投资分析报告 - {datetime.utcnow().strftime('%Y-%m-%d')}")
    
    if st.button("生成HTML报告"):
        with st.spinner("汇总数据..."):
            report_date = report_date_opt.strip() or None
            if report_date is None:
                report_date = _get_latest_report_date_for_slug(slugs[0])
            portfolios = _collect_portfolios(slugs, report_date)
            clue_srv = InvestmentClueService()
            consensus = clue_srv.find_consensus_picks(portfolios, min_institutions=min_inst)
            anomalies = _unusual_trades(slugs, datetime.utcnow() - timedelta(days=days))
            anomalies = clue_srv.detect_unusual_trades(anomalies)
            rec_srv = InvestmentRecommendationService()
            recs = rec_srv.generate_recommendations(consensus=consensus, anomalies=anomalies, 
                                                  style=None, top_k=10)

        html = ReportService().build_html_report(title=title, consensus=consensus, 
                                                anomalies=anomalies, recommendations=recs)
        st.download_button(
            label="下载HTML报告",
            data=html,
            file_name=f"report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.html",
            mime="text/html",
        )


if __name__ == "__main__":
    main()
