import sys
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

import streamlit as st

# 确保根目录可导入
APP_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = APP_DIR.parent
for p in [str(PROJECT_ROOT), str(APP_DIR)]:
    if p not in sys.path:
        sys.path.insert(0, p)

from data.profit import ProfitDataClient
from services import PortfolioAnalysisService, InvestmentStyleService
from services.investment_clue_service import InvestmentClueService
from services.investment_recommendation_service import InvestmentRecommendationService
from services.institutional_tracking_service import InstitutionalTrackingService
from services.report_service import ReportService
from services.user_settings_service import UserSettingsService
from services import InstitutionalTaggingService
from services import StyleSimilarityService
from models import Portfolio, StockHolding, PortfolioMetrics, InvestmentStyle


@st.cache_resource(show_spinner=False)
def get_profit_interface() -> ProfitDataClient:
    return ProfitDataClient()


def render_sidebar(session: Dict[str, Any]):
    st.sidebar.header("机构投资分析 - 控制台")
    usvc = UserSettingsService()
    cur = usvc.load()
    with st.sidebar.expander("用户设置", expanded=False):
        risk = st.selectbox(
            "风险偏好", ["conservative", "moderate", "aggressive"],
            index=["conservative", "moderate", "aggressive"].index(cur.get("risk_profile", "moderate"))
        )
        min_inst = st.number_input("最少共识机构数", min_value=1, max_value=10, value=int(cur.get("min_consensus_institutions", 2)))
        days = st.number_input("异常交易窗口(天)", min_value=7, max_value=365, value=int(cur.get("anomaly_window_days", 90)))
        if st.button("保存设置"):
            usvc.save({
                "risk_profile": risk,
                "min_consensus_institutions": int(min_inst),
                "anomaly_window_days": int(days),
            })
            st.success("设置已保存")
    st.sidebar.divider()
    time_window_days = st.sidebar.slider("交易窗口(天)", 7, 180, value=session.get("trade_days", 90))
    st.sidebar.caption("用于线索发现的时间窗口")
    session["trade_days"] = time_window_days


def render_investor_card(item: Dict[str, Any]):
    cols = st.columns([1, 3, 2])
    with cols[0]:
        logo = item.get("logo_url") or item.get("logo")
        if logo:
            st.image(logo, width=72)
    with cols[1]:
        st.subheader(item.get("investor") or item.get("name") or item.get("company") or item.get("slug", "未知机构"))
        st.caption(
            f"slug: {item.get('slug', '-')} | CIK: {item.get('cik', '-')} | 类型: {item.get('type') or item.get('investor_type', '-')}"
        )
        if item.get("description"):
            st.write(item["description"][:160] + ("..." if len(item["description"]) > 160 else ""))
        elif item.get("company"):
            st.write(item.get("company"))
    with cols[2]:
        slug_or_cik = item.get('slug') or item.get('cik') or ''
        key_track = f"sel_{slug_or_cik}"
        if st.button("加入对比/跟踪", key=key_track, use_container_width=True):
            tracked = st.session_state.setdefault("tracked_slugs", set())
            slug = item.get("slug") or item.get("cik")
            if slug:
                tracked.add(slug)
                st.success(f"已添加: {slug}")
        key_detail = f"detail_{slug_or_cik}"
        if st.button("查看详情", key=key_detail, use_container_width=True):
            st.session_state["detail_slug"] = item.get("slug") or item.get("cik")
            st.session_state["detail_item"] = item
            st.switch_page("pages/机构详情.py")


@st.cache_data(show_spinner=False)
def get_latest_report_date_for_slug(slug: str) -> Optional[str]:
    iface = get_profit_interface()
    try:
        dates = iface.get_investors_reporting_dates(investor_type="hedge_fund_manager", slug=slug)
        if isinstance(dates, dict) and dates.get("reporting_dates"):
            return sorted(dates["reporting_dates"]) [-1]
        if isinstance(dates, list) and dates:
            return sorted(dates)[-1]
    except Exception:
        return None
    return None


@st.cache_data(show_spinner=False)
def get_reporting_dates(slug: str) -> List[str]:
    iface = get_profit_interface()
    try:
        dates = iface.get_investors_reporting_dates(investor_type="hedge_fund_manager", slug=slug)
        if isinstance(dates, dict) and dates.get("reporting_dates"):
            return sorted(dates["reporting_dates"])  # type: ignore
        if isinstance(dates, list):
            return sorted(dates)
    except Exception:
        return []
    return []


def collect_portfolios(slugs: List[str], report_date: Optional[str]) -> Dict[str, Any]:
    iface = get_profit_interface()
    portfolios: Dict[str, Any] = {}
    for slug in slugs:
        try:
            data = iface.get_investor_portfolio(slug=slug, report_date=report_date)
            portfolios[slug] = data
        except Exception as e:
            portfolios[slug] = {"error": str(e)}
    return portfolios


def to_portfolio(slug: str, raw: Dict[str, Any]) -> Optional[Portfolio]:
    if not isinstance(raw, dict) or not raw:
        return None
    items = raw.get("stock_holdings") or raw.get("holdings") or raw.get("positions") or []
    holdings: List[StockHolding] = []
    for h in items:
        holdings.append(
            StockHolding(
                ticker=h.get("ticker") or h.get("symbol"),
                company_name=h.get("company_name") or h.get("name"),
                cusip=h.get("cusip"),
                shares=(h.get("shares") or h.get("qty") or 0),
                value=(h.get("value") or h.get("market_value") or 0.0),
                portfolio_ratio=(h.get("portfolio_ratio") or h.get("weight") or 0.0),
                sector=h.get("sector"),
                price=h.get("price") or None,
                roi=h.get("roi") or None,
                change_in_shares=h.get("change_in_shares") or None,
                change_in_value=h.get("change_in_value") or None,
            )
        )
    total_mv = float(raw.get("total_market_value") or raw.get("portfolio_value") or 0.0)
    report_date = raw.get("report_date") or raw.get("as_of_date")
    filing_date = raw.get("filing_date") or raw.get("filed_at")
    metrics = PortfolioMetrics(
        concentration_ratio_top10=sum(sorted([float(h.portfolio_ratio or 0.0) for h in holdings], reverse=True)[:10]),
        total_market_value=total_mv,
        positions_count=len(holdings),
    )
    sector_sum: Dict[str, float] = {}
    total_val = 0.0
    for h in holdings:
        v = float(h.value or 0.0)
        total_val += v
        if h.sector:
            sector_sum[h.sector] = sector_sum.get(h.sector, 0.0) + v
    sector_breakdown = {k: (v / total_val if total_val > 0 else 0.0) for k, v in sector_sum.items()}
    return Portfolio(
        investor_slug=slug,
        report_date=datetime.fromisoformat(report_date) if isinstance(report_date, str) else None,
        filing_date=datetime.fromisoformat(filing_date) if isinstance(filing_date, str) else None,
        total_market_value=total_mv,
        stock_holdings=holdings,
        sector_breakdown=sector_breakdown,
        portfolio_metrics=metrics,
    )



