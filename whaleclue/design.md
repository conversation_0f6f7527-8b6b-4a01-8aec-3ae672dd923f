# 机构投资风格识别与投资线索发掘设计文档

## 概述

机构投资风格识别与投资线索发掘系统是一个基于 AI 和 profit.py API 的智能投资分析平台。该系统通过深度分析机构投资者的持仓数据、交易行为和投资组合变化，使用机器学习算法识别投资风格模式，为用户提供跟投参考和投资机会发现功能。

## 架构

### 整体架构

```mermaid
graph TB
    subgraph "前端展示层"
        A[机构搜索页面] --> B[投资风格分析页面]
        A --> C[持仓分析页面]
        A --> D[投资线索页面]
        A --> E[机构对比页面]
        A --> F[跟踪监控页面]
        A --> G[投资建议页面]
    end
    
    subgraph "业务逻辑层"
        H[机构数据服务] --> I[投资风格识别服务]
        H --> J[持仓分析服务]
        H --> K[投资线索发掘服务]
        H --> L[机构标签服务]
        H --> M[跟踪监控服务]
        H --> N[投资建议服务]
        I --> O[AI 风格分析引擎]
        K --> P[AI 线索发掘引擎]
    end
    
    subgraph "数据访问层"
        Q[Profit API 接口] --> R[机构数据缓存]
        Q --> S[持仓数据存储]
        Q --> T[交易记录存储]
        Q --> U[风格标签存储]
    end
    
    subgraph "外部服务"
        V[Profit.com API]
        W[AI 模型服务]
        X[通知服务]
    end
    
    B --> I
    C --> J
    D --> K
    E --> I
    F --> M
    G --> N
    
    H --> Q
    I --> Q
    J --> Q
    K --> Q
    L --> Q
    M --> Q
    N --> Q
    
    O --> W
    P --> W
    Q --> V
    M --> X
```

### 技术栈

- **前端框架**: Streamlit 1.48+
- **AI/ML 框架**: scikit-learn, pandas, numpy
- **数据处理**: pandas, numpy, scipy
- **API 客户端**: httpx (profit.py)
- **数据库**: MongoDB (持仓数据), Redis (缓存)
- **可视化**: Plotly, Seaborn
- **通知服务**: 邮件/短信 API
- **任务调度**: APScheduler

## 组件和接口

### 1. 数据获取层

#### Profit API 接口封装 (`data/profit_data_interface.py`)
```python
class ProfitDataInterface:
    def __init__(self, client: ProfitDataClient):
        self.client = client
        self.cache_manager = CacheManager()
    
    async def get_investor_info(self, slug: str = None, cik: str = None) -> InvestorInfo:
        """获取机构投资者基础信息"""
        
    async def get_investor_portfolio(self, slug: str, report_dates: List[str]) -> List[Portfolio]:
        """获取机构投资组合数据"""
        
    async def get_investor_trades(self, slug: str, time_range: str) -> List[Trade]:
        """获取机构交易记录"""
        
    async def get_investor_performance(self, slug: str, time_range: str) -> PerformanceData:
        """获取机构投资表现数据"""
        
    async def search_investors(self, query: str, investor_type: str) -> List[InvestorInfo]:
        """搜索机构投资者"""
        
    async def get_batch_portfolios(self, slugs: List[str], report_date: str) -> Dict[str, Portfolio]:
        """批量获取机构投资组合"""
```

#### 数据缓存管理器 (`data/institutional_cache_manager.py`)
```python
class InstitutionalCacheManager:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.default_ttl = 3600  # 1小时
        
    def cache_investor_data(self, key: str, data: Any, ttl: int = None):
        """缓存机构数据"""
        
    def get_cached_investor_data(self, key: str) -> Optional[Any]:
        """获取缓存的机构数据"""
        
    def invalidate_investor_cache(self, pattern: str):
        """清除机构相关缓存"""
        
    def cache_analysis_result(self, analysis_key: str, result: Any, ttl: int = 7200):
        """缓存分析结果"""
```

### 2. 核心业务服务层

#### 机构数据服务 (`services/institutional_data_service.py`)
```python
class InstitutionalDataService:
    def __init__(self, profit_interface: ProfitDataInterface):
        self.profit_api = profit_interface
        self.db = MongoDBManager()
        
    async def sync_investor_data(self, slug: str) -> InvestorProfile:
        """同步机构投资者数据"""
        
    async def get_investor_profile(self, slug: str) -> InvestorProfile:
        """获取机构投资者完整档案"""
        
    async def get_portfolio_history(self, slug: str, periods: int = 8) -> List[Portfolio]:
        """获取机构投资组合历史"""
        
    async def calculate_portfolio_metrics(self, portfolio: Portfolio) -> PortfolioMetrics:
        """计算投资组合指标"""
        
    async def detect_portfolio_changes(self, current: Portfolio, previous: Portfolio) -> PortfolioChanges:
        """检测投资组合变化"""
```

#### 投资风格识别服务 (`services/investment_style_service.py`)
```python
class InvestmentStyleService:
    def __init__(self):
        self.style_classifier = StyleClassifier()
        self.feature_extractor = FeatureExtractor()
        
    def extract_style_features(self, investor_data: InvestorProfile) -> StyleFeatures:
        """提取投资风格特征"""
        # 特征包括：
        # - 持仓集中度 (concentration_ratio)
        # - 行业分散度 (sector_diversification)
        # - 市值偏好 (market_cap_preference)
        # - 持仓周期 (holding_period)
        # - 换手率 (turnover_rate)
        # - 价值/成长指标 (value_growth_score)
        
    def classify_investment_style(self, features: StyleFeatures) -> InvestmentStyle:
        """分类投资风格"""
        # 使用机器学习模型分类：
        # - 价值投资 (Value)
        # - 成长投资 (Growth)
        # - 平衡投资 (Balanced)
        # - 动量投资 (Momentum)
        # - 质量投资 (Quality)
        
    def calculate_style_confidence(self, features: StyleFeatures, style: InvestmentStyle) -> float:
        """计算风格置信度"""
        
    def track_style_evolution(self, slug: str, periods: int = 12) -> StyleEvolution:
        """追踪投资风格演变"""
        
    def compare_investment_styles(self, slugs: List[str]) -> StyleComparison:
        """对比投资风格"""
```

#### 持仓分析服务 (`services/portfolio_analysis_service.py`)
```python
class PortfolioAnalysisService:
    def __init__(self):
        self.risk_calculator = RiskCalculator()
        self.performance_analyzer = PerformanceAnalyzer()
        
    def analyze_portfolio_composition(self, portfolio: Portfolio) -> CompositionAnalysis:
        """分析投资组合构成"""
        
    def calculate_risk_metrics(self, portfolio: Portfolio, benchmark: str = "SPY") -> RiskMetrics:
        """计算风险指标"""
        # 包括：波动率、贝塔、夏普比率、最大回撤等
        
    def analyze_sector_allocation(self, portfolio: Portfolio) -> SectorAllocation:
        """分析行业配置"""
        
    def detect_concentration_risk(self, portfolio: Portfolio) -> ConcentrationRisk:
        """检测集中度风险"""
        
    def calculate_performance_attribution(self, portfolio: Portfolio, benchmark: str) -> PerformanceAttribution:
        """计算业绩归因"""
        
    def generate_portfolio_summary(self, portfolio: Portfolio) -> PortfolioSummary:
        """生成投资组合摘要"""
```

#### 投资线索发掘服务 (`services/investment_clue_service.py`)
```python
class InvestmentClueService:
    def __init__(self):
        self.anomaly_detector = AnomalyDetector()
        self.consensus_analyzer = ConsensusAnalyzer()
        
    def detect_unusual_trades(self, trades: List[Trade], threshold: float = 2.0) -> List[UnusualTrade]:
        """检测异常交易"""
        
    def find_consensus_picks(self, portfolios: Dict[str, Portfolio], min_institutions: int = 3) -> List[ConsensusPick]:
        """发现机构共识股票"""
        
    def track_smart_money_moves(self, top_performers: List[str], days_back: int = 30) -> List[SmartMoneyMove]:
        """追踪聪明钱动向"""
        
    def identify_emerging_themes(self, portfolios: Dict[str, Portfolio]) -> List[InvestmentTheme]:
        """识别新兴投资主题"""
        
    def generate_investment_signals(self, clues: List[InvestmentClue]) -> List[InvestmentSignal]:
        """生成投资信号"""
        
    def rank_investment_opportunities(self, signals: List[InvestmentSignal]) -> List[RankedOpportunity]:
        """排序投资机会"""
```

#### 机构标签服务 (`services/institutional_tagging_service.py`)
```python
class InstitutionalTaggingService:
    def __init__(self):
        self.tag_classifier = TagClassifier()
        self.tag_weights = TagWeights()
        
    def generate_style_tags(self, style: InvestmentStyle) -> List[StyleTag]:
        """生成投资风格标签"""
        
    def generate_behavior_tags(self, trading_behavior: TradingBehavior) -> List[BehaviorTag]:
        """生成交易行为标签"""
        
    def generate_performance_tags(self, performance: PerformanceData) -> List[PerformanceTag]:
        """生成业绩表现标签"""
        
    def generate_sector_expertise_tags(self, portfolio_history: List[Portfolio]) -> List[ExpertiseTag]:
        """生成行业专精标签"""
        
    def update_tag_weights(self, slug: str, new_data: InvestorProfile):
        """更新标签权重"""
        
    def get_similar_institutions(self, slug: str, similarity_threshold: float = 0.7) -> List[SimilarInstitution]:
        """获取相似机构"""
```

### 3. AI 分析引擎

#### 投资风格分类器 (`ai/style_classifier.py`)
```python
class StyleClassifier:
    def __init__(self):
        self.model = self._load_or_train_model()
        self.feature_scaler = StandardScaler()
        
    def _extract_features(self, investor_data: InvestorProfile) -> np.ndarray:
        """提取用于分类的特征向量"""
        features = [
            investor_data.concentration_ratio,
            investor_data.sector_diversification,
            investor_data.avg_market_cap,
            investor_data.avg_holding_period,
            investor_data.turnover_rate,
            investor_data.value_score,
            investor_data.growth_score,
            investor_data.momentum_score,
            investor_data.quality_score,
            investor_data.volatility,
            investor_data.beta,
            investor_data.sharpe_ratio
        ]
        return np.array(features).reshape(1, -1)
        
    def classify(self, investor_data: InvestorProfile) -> Tuple[str, float]:
        """分类投资风格并返回置信度"""
        
    def get_style_probabilities(self, investor_data: InvestorProfile) -> Dict[str, float]:
        """获取各种风格的概率分布"""
        
    def retrain_model(self, training_data: List[Tuple[InvestorProfile, str]]):
        """重新训练模型"""
```

#### 异常检测器 (`ai/anomaly_detector.py`)
```python
class AnomalyDetector:
    def __init__(self):
        self.isolation_forest = IsolationForest(contamination=0.1)
        self.statistical_detector = StatisticalAnomalyDetector()
        
    def detect_trading_anomalies(self, trades: List[Trade]) -> List[AnomalyScore]:
        """检测交易异常"""
        
    def detect_portfolio_anomalies(self, portfolio: Portfolio, historical_portfolios: List[Portfolio]) -> List[PortfolioAnomaly]:
        """检测投资组合异常"""
        
    def detect_performance_anomalies(self, performance: PerformanceData) -> List[PerformanceAnomaly]:
        """检测业绩异常"""
        
    def calculate_anomaly_significance(self, anomaly: Anomaly) -> float:
        """计算异常重要性"""
```

### 4. 前端页面组件

#### 机构搜索页面 (`pages/institutional_search.py`)
```python
class InstitutionalSearchPage:
    def render_search_interface(self):
        """渲染搜索界面"""
        
    def render_filter_options(self):
        """渲染筛选选项"""
        
    def render_search_results(self, results: List[InvestorInfo]):
        """渲染搜索结果"""
        
    def render_institution_card(self, investor: InvestorInfo):
        """渲染机构卡片"""
```

#### 投资风格分析页面 (`pages/investment_style_analysis.py`)
```python
class InvestmentStyleAnalysisPage:
    def render_style_overview(self, style_analysis: StyleAnalysis):
        """渲染风格概览"""
        
    def render_style_radar_chart(self, style_features: StyleFeatures):
        """渲染风格雷达图"""
        
    def render_style_evolution_chart(self, evolution: StyleEvolution):
        """渲染风格演变图"""
        
    def render_peer_comparison(self, comparison: StyleComparison):
        """渲染同行对比"""
```

#### 投资线索页面 (`pages/investment_clues.py`)
```python
class InvestmentCluesPage:
    def render_clue_dashboard(self, clues: List[InvestmentClue]):
        """渲染线索仪表板"""
        
    def render_consensus_picks(self, picks: List[ConsensusPick]):
        """渲染共识股票"""
        
    def render_unusual_activities(self, activities: List[UnusualTrade]):
        """渲染异常活动"""
        
    def render_smart_money_moves(self, moves: List[SmartMoneyMove]):
        """渲染聪明钱动向"""
```

## 数据模型

### 机构投资者模型
```python
@dataclass
class InvestorInfo:
    slug: str
    cik: str
    name: str
    company: str
    investor_type: str  # hedge_fund_manager, corporate_insider
    description: str
    logo_url: str
    aum: Optional[float]  # Assets Under Management
    founded_year: Optional[int]
    location: Optional[str]
    website: Optional[str]

@dataclass
class InvestorProfile:
    investor_info: InvestorInfo
    current_portfolio: Portfolio
    portfolio_history: List[Portfolio]
    trading_history: List[Trade]
    performance_data: PerformanceData
    style_analysis: StyleAnalysis
    tags: List[Tag]
    last_updated: datetime
```

### 投资组合模型
```python
@dataclass
class Portfolio:
    investor_slug: str
    report_date: datetime
    filing_date: datetime
    total_market_value: float
    stock_holdings: List[StockHolding]
    bond_holdings: List[BondHolding]
    option_holdings: List[OptionHolding]
    sector_breakdown: List[SectorAllocation]
    portfolio_metrics: PortfolioMetrics
    qoq_change: float  # Quarter over Quarter change
    roi: float

@dataclass
class StockHolding:
    ticker: str
    company_name: str
    cusip: str
    shares: int
    value: float
    portfolio_ratio: float
    sector: str
    price: float
    roi: float
    change_in_shares: int
    change_in_value: float
    avg_price: float
    share_history: List[int]
    value_history: List[float]
```

### 投资风格模型
```python
@dataclass
class StyleFeatures:
    concentration_ratio: float  # 前10大持仓占比
    sector_diversification: float  # 行业分散度
    market_cap_preference: str  # large, mid, small
    avg_holding_period: float  # 平均持仓期（年）
    turnover_rate: float  # 换手率
    value_score: float  # 价值投资评分
    growth_score: float  # 成长投资评分
    momentum_score: float  # 动量投资评分
    quality_score: float  # 质量投资评分
    volatility: float  # 投资组合波动率
    beta: float  # 贝塔系数
    sharpe_ratio: float  # 夏普比率

@dataclass
class InvestmentStyle:
    primary_style: str  # Value, Growth, Balanced, Momentum, Quality
    secondary_style: Optional[str]
    style_confidence: float
    style_scores: Dict[str, float]
    market_cap_preference: str
    sector_preferences: List[str]
    geographic_preference: str
    
@dataclass
class StyleEvolution:
    investor_slug: str
    time_periods: List[datetime]
    style_changes: List[InvestmentStyle]
    stability_score: float
    trend_direction: str
```

### 投资线索模型
```python
@dataclass
class InvestmentClue:
    clue_id: str
    clue_type: str  # consensus_pick, unusual_trade, smart_money_move
    ticker: str
    company_name: str
    description: str
    confidence_score: float
    supporting_evidence: List[str]
    related_institutions: List[str]
    detected_at: datetime
    
@dataclass
class ConsensusPick:
    ticker: str
    company_name: str
    institution_count: int
    total_value: float
    avg_portfolio_weight: float
    institutions: List[str]
    consensus_strength: float
    
@dataclass
class UnusualTrade:
    investor_slug: str
    ticker: str
    trade_type: str  # buy, sell, new_position
    trade_value: float
    portfolio_impact: float
    anomaly_score: float
    trade_date: datetime
```

### 标签系统模型
```python
@dataclass
class Tag:
    tag_id: str
    tag_type: str  # style, behavior, performance, expertise
    tag_name: str
    tag_value: str
    confidence: float
    weight: float
    created_at: datetime
    
@dataclass
class StyleTag(Tag):
    style_category: str  # value, growth, momentum, etc.
    
@dataclass
class BehaviorTag(Tag):
    behavior_category: str  # frequency, holding_period, etc.
    
@dataclass
class PerformanceTag(Tag):
    performance_metric: str  # returns, volatility, sharpe, etc.
    
@dataclass
class ExpertiseTag(Tag):
    sector: str
    expertise_level: float
```

## 错误处理

### 异常类型定义
```python
class InstitutionalAnalysisException(Exception):
    """机构分析基础异常"""
    pass

class DataSyncException(InstitutionalAnalysisException):
    """数据同步异常"""
    pass

class StyleClassificationException(InstitutionalAnalysisException):
    """风格分类异常"""
    pass

class InvestmentClueException(InstitutionalAnalysisException):
    """投资线索异常"""
    pass

class APIRateLimitException(InstitutionalAnalysisException):
    """API 限流异常"""
    pass
```

### 错误处理策略
1. **API 调用重试**: 对 profit.com API 调用实施指数退避重试
2. **数据验证**: 对获取的数据进行完整性和合理性验证
3. **优雅降级**: AI 分析失败时提供基础统计分析
4. **缓存容错**: 缓存失效时直接调用 API 获取数据
5. **用户友好提示**: 将技术错误转换为用户可理解的消息

## 测试策略

### 单元测试
- 投资风格分类算法的准确性测试
- 异常检测算法的敏感性测试
- 数据处理和特征提取的正确性测试
- 缓存机制的一致性测试

### 集成测试
- Profit API 接口的数据获取测试
- 数据库存储和查询的完整性测试
- AI 模型预测结果的稳定性测试
- 前端页面的交互功能测试

### 性能测试
- 大量机构数据处理的性能测试
- 并发用户访问的响应时间测试
- AI 模型推理的吞吐量测试
- 缓存命中率和效果测试

### 数据质量测试
- 机构数据的完整性和准确性验证
- 投资风格分类结果的合理性检查
- 投资线索的有效性回测
- 标签系统的一致性验证