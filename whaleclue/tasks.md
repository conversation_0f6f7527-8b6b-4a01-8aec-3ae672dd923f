# 机构投资风格识别与投资线索发掘实施计划

- [ ] 1. 项目基础设施和数据层搭建
  - 创建项目目录结构，包含 data、services、ai、pages 等模块
  - 配置 MongoDB 数据库连接和集合结构
  - 设置 Redis 缓存连接和键命名规范
  - _需求: 需求 1.1, 需求 12.4_

- [ ] 2. Profit API 数据接口实现
  - [x] 2.1 Profit API 接入方式
    - [x] 直接使用 profit.py 的 ProfitDataClient（已去除多余封装）
    - [x] 实现机构搜索、报告期、组合与交易数据的直接调用
    - _需求: 需求 1.1, 需求 1.2_

  - [x] 2.2 实现数据缓存管理器
    - [x] 创建 InstitutionalCacheManager 类处理机构数据缓存（内存版，可替换 Redis）
    - [x] 实现缓存键生成策略和 TTL 管理
    - [x] 添加批量缓存操作和缓存失效机制
    - _需求: 需求 1.5, 需求 12.4_

- [x] 2.3 实现数据同步和存储服务
  - [x] 创建机构数据同步（单机构调用版，后续补 cron/批量）
  - [x] 实现基础清洗与字段归一（组合与持仓）
  - [ ] 增量与版本控制留待完善
    - _需求: 需求 1.3, 需求 10.1_

- [-] 3. 核心数据模型和存储实现
  - [x] 3.1 定义机构投资者数据模型
    - [x] 创建 InvestorInfo、InvestorProfile 等核心数据类
    - [x] 实现 Portfolio、StockHolding 等投资组合模型
    - [ ] 添加数据验证和序列化方法（现提供基础 asdict）
    - _需求: 需求 1.1, 需求 2.1_

  - [ ] 3.2 实现投资风格和标签数据模型
    - 创建 StyleFeatures、InvestmentStyle 等风格分析模型
    - 实现 Tag、StyleTag 等多维度标签模型
    - 添加投资线索相关的 InvestmentClue、ConsensusPick 模型
    - _需求: 需求 3.1, 需求 4.1, 需求 5.1_

- [ ] 4. 投资组合分析服务开发
  - [x] 4.1 实现持仓数据分析服务
    - [x] 创建 PortfolioAnalysisService 类
    - [x] 实现投资组合构成分析和行业配置计算
    - [x] 添加基础风险指标计算（波动率、Sharpe、最大回撤）
    - _需求: 需求 2.1, 需求 2.2, 需求 2.4_

  - [ ] 4.2 实现投资组合变化检测
    - [x] 开发持仓变化检测（新仓/增持/减持/清仓）基本能力
    - [ ] 重要性评分与趋势分析待补
    - _需求: 需求 2.3, 需求 6.3_

  - [ ] 4.3 实现风险收益指标计算
    - 开发投资组合风险指标计算（波动率、贝塔、夏普比率）
    - 实现业绩归因分析和基准对比功能
    - 添加风险调整收益和最大回撤计算
    - _需求: 需求 2.4, 需求 7.3_

- [ ] 5. AI 投资风格识别引擎
  - [x] 5.1 实现投资风格特征提取器（轻量规则版）
    - [x] 提取集中度、行业分散度等特征（占位：换手率/价值成长等可后续补强）
    - _需求: 需求 3.1, 需求 3.2_

  - [x] 5.2 开发投资风格分类器（启发式）
    - [x] 实现价值、成长、平衡、动量、质量等风格的得分与主要风格输出
    - [ ] 置信度与概率分布后续用 ML 模型替换
    - _需求: 需求 3.2, 需求 3.4_

  - [ ] 5.3 实现投资风格演变追踪
    - 开发风格变化检测算法，追踪投资风格演变
    - 实现风格稳定性评分和趋势方向判断
    - 添加风格转换事件识别和重要性评估
    - _需求: 需求 3.5, 需求 6.3_

- [ ] 6. 机构标签系统实现
  - [ ] 6.1 实现多维度标签生成器
    - 创建 InstitutionalTaggingService 类
    - 实现投资风格标签、交易行为标签生成
    - 添加业绩表现标签和行业专精标签生成
    - _需求: 需求 4.1, 需求 4.2, 需求 4.3_

  - [ ] 6.2 实现标签权重和相似度计算
    - 开发标签权重动态调整算法
    - 实现机构相似度计算和聚类分析
    - 添加标签相关性分析和推荐功能
    - _需求: 需求 4.4, 需求 4.5_

- [ ] 7. 投资线索发掘引擎
  - [x] 7.1 实现异常交易检测器（轻量规则版）
    - [x] 基于分位阈值识别大额异常交易（Top 10%）
    - [ ] 异常重要性评分与置信度后续增强
    - _需求: 需求 5.1, 需求 10.2_

  - [x] 7.2 开发机构共识分析器（基础版）
    - [x] 计算多机构持仓重叠度，输出共识列表
    - [ ] 共识强度评分与趋势分析后续增强
    - _需求: 需求 5.2, 需求 7.2_

  - [x] 7.3 实现聪明钱追踪系统（简版）
    - [x] 交集：异常交易与共识标的的交叠作为重点跟踪
    - [ ] 明星机构识别、主题与趋势待完善
    - _需求: 需求 5.3, 需求 5.4_

- [ ] 8. 投资建议生成服务
  - [x] 8.1 实现智能投资建议引擎（启发式版）
    - [x] 创建 InvestmentRecommendationService 类
    - [x] 基于共识与异常线索生成个股推荐、逻辑、风险与持有期限
    - _需求: 需求 8.1, 需求 8.2, 需求 8.4_

  - [ ] 8.2 开发个性化推荐系统
    - [x] 实现用户风险偏好对评分的权重调节
    - [ ] 机构风格匹配与个性化策略建议
    - 开发投资策略建议和组合配置建议
    - 添加投资逻辑说明和风险提示生成
    - _需求: 需求 8.3, 需求 8.5, 需求 11.3_

- [ ] 9. 机构跟踪监控系统
  - [x] 9.1 实现机构跟踪服务（基础版）
    - [x] 创建 InstitutionalTrackingService（本地 watchlist 持久化）
    - [x] 自定义跟踪列表管理、最近交易时间线、最近两期持仓变化摘要
    - _需求: 需求 6.1, 需求 6.2_

  - [ ] 9.2 开发实时通知系统
    - 实现重要事件检测和通知触发机制
    - 开发多渠道通知服务（邮件、短信、应用内）
    - 添加通知个性化设置和频率控制
    - _需求: 需求 6.3, 需求 10.3, 需求 10.4_

- [ ] 10. Streamlit 前端页面开发
  - [x] 10.1 创建机构搜索和浏览页面
    - [x] 实现机构投资者搜索界面和筛选功能
    - [x] 创建机构信息卡片展示（详情页待后续）
    - [x] 添加机构对比选择和批量操作（加入跟踪列表）
    - _需求: 需求 1.4, 需求 11.1_

  - [x] 10.2 实现投资风格分析页面（基础版）
    - [x] 创建风格可视化界面，展示风格表与雷达图
    - [x] 增加风格演变趋势（近6期）
    - [ ] 对比分析与相似机构推荐后续补充
    - _需求: 需求 3.3, 需求 7.1, 需求 9.3_

  - [ ] 10.3 开发持仓分析页面
    - 实现投资组合构成可视化展示
    - 创建持仓变化追踪和历史对比功能
    - 添加风险指标展示和业绩分析图表
    - _需求: 需求 2.5, 需求 6.4, 需求 9.1_

- [ ] 11. 投资线索和建议页面
  - [x] 11.1 创建投资线索发现页面
    - [x] 实现投资线索仪表板和分类展示（最小可用：共识股票、异常交易）
    - [x] 创建异常交易、共识股票展示（聪明钱动向后续补充）
    - [ ] 添加线索详情查看和相关分析功能
    - _需求: 需求 5.5, 需求 9.2_

- [x] 11.2 实现投资建议页面（基础版）
  - [x] 创建建议展示界面（列表 + 逻辑 + 风险 + 持有期限）
  - [ ] 个性化偏好匹配与更详细分析待完善
    - _需求: 需求 8.5, 需求 9.4_

- [x] 11.3 开发机构跟踪页面（基础版）
  - [x] 跟踪列表管理（多选 + 保存）
  - [x] 最近交易时间线与持仓变化展示
  - [ ] 跟踪统计与个性化设置后续完善
    - _需求: 需求 6.4, 需求 6.5, 需求 11.2_

- [ ] 12. 数据可视化和报告功能
  - [x] 12.1 实现交互式图表组件（基础版）
    - [x] 使用 Plotly 创建行业占比饼图、持仓表格
    - [ ] 机构对比与多维图表后续补充
    - _需求: 需求 9.1, 需求 9.4_

  - [x] 12.2 开发报告生成系统（HTML基础版）
    - [x] 创建报告模板（共识/异常/建议）与下载
    - [ ] PDF 导出与分享后续补充
    - _需求: 需求 9.5, 需求 6.5_

- [ ] 13. 用户个性化和设置
  - [x] 13.1 实现用户偏好设置系统（基础版）
    - [x] 创建设置管理界面（侧边栏折叠区）与本地持久化
    - [x] 共识阈值、异常窗口、风险偏好联动线索与建议
    - [ ] 机构类型与风格偏好筛选（部分已默认应用）
    - _需求: 需求 11.1, 需求 11.2_

  - [ ] 13.2 开发个性化推荐引擎
    - 实现基于用户行为的机构推荐算法
    - 开发个性化投资线索筛选和排序
    - 添加用户历史记录和偏好学习功能
    - _需求: 需求 11.4, 需求 11.5_

- [ ] 14. 性能优化和错误处理
  - [ ] 14.1 实现系统性能优化
    - 优化数据库查询和索引设计
    - 实现 AI 模型推理的批处理和缓存
    - 添加异步任务处理和队列管理
    - _需求: 需求 12.1, 需求 12.2_

  - [ ] 14.2 完善错误处理和监控
    - 实现全局异常处理和用户友好错误提示
    - 添加 API 调用重试和熔断机制
    - 创建系统监控和日志记录功能
    - _需求: 需求 12.3, 需求 12.5_

- [ ] 15. 测试和质量保证
  - [ ] 15.1 编写单元测试
    - 为所有服务类和 AI 算法创建单元测试
    - 实现数据模型验证和边界条件测试
    - 添加投资风格分类和线索发现的准确性测试
    - _需求: 所有功能需求的验证_

  - [ ] 15.2 编写集成测试
    - 创建端到端的用户工作流测试
    - 实现 Profit API 集成和数据同步测试
    - 添加前端页面交互和数据展示测试
    - _需求: 系统集成和用户体验验证_

- [ ] 16. 部署和文档
  - [ ] 16.1 准备生产部署配置
    - 创建 Docker 容器化配置和环境变量管理
    - 实现数据库迁移脚本和初始化配置
    - 添加健康检查和监控端点
    - _需求: 系统部署和运维_

  - [ ] 16.2 编写用户和开发文档
    - 创建用户使用指南和功能说明文档
    - 编写 API 文档和开发者部署指南
    - 添加故障排除和维护操作文档
    - _需求: 用户体验和系统维护_