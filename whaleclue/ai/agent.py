from __future__ import annotations

from typing import List, Dict, Any

try:
    # Attempt to use agno agent if available
    from agno.agent import Agent  # type: ignore
    AGNO_AVAILABLE = True
except Exception:  # pragma: no cover
    Agent = None
    AGNO_AVAILABLE = False


def _format_top_picks_md(picks: List[Dict[str, Any]]) -> str:
    lines = ["### 机构共识要点"]
    for p in picks[:10]:
        lines.append(
            f"- {p['ticker']}: 共有 {p['institution_count']} 家机构持有，平均权重 {p['avg_portfolio_weight']:.2f}%，合计持有额约 {p['total_value']:.0f}"
        )
    return "\n".join(lines)


def _format_anomalies_md(anomalies: List[Dict[str, Any]]) -> str:
    lines = ["### 异常交易要点"]
    for t in anomalies[:10]:
        lines.append(
            f"- {t['trade_date']} {t['investor_slug']} -> {t['ticker']} {t['trade_type']} 交易额约 {t['trade_value']:.0f}"
        )
    return "\n".join(lines)


def get_insights_with_agent(picks: List[Dict[str, Any]], anomalies: List[Dict[str, Any]]) -> str:
    """Return markdown insights. Use agno Agent when available, fallback to heuristic summary."""
    context = _format_top_picks_md(picks) + "\n\n" + _format_anomalies_md(anomalies)

    if AGNO_AVAILABLE and Agent is not None:  # pragma: no cover
        try:
            prompt = (
                "请基于以下‘机构共识’与‘异常交易’要点，总结3-5条可以执行的投资洞见，"
                "并分别给出：投资逻辑、关键假设、潜在风险、建议持有期限。\n\n" + context
            )
            agent = Agent()
            result = agent.run(prompt)
            if isinstance(result, str):
                return result
        except Exception:
            pass

    # Fallback summarization
    lines = [
        "## AI 洞见 (轻量规则生成)",
        "- 关注‘多机构共识且平均权重较高’的标的，优先跟踪榜首2-3只；",
        "- 对‘大额买入/新建仓’的异常交易进行二次验证（基本面、催化剂、财报），择机跟随；",
        "- 对‘多机构集中买入’但权重偏低的标的，考虑分批布局以获取期权性；",
        "- 若同一标的同时出现机构增持与显著异常交易，可设置事件驱动型观察清单；",
        "- 注意流动性与回撤风险，设置分级止损与仓位上限。",
        "\n",
        context,
    ]
    return "\n".join(lines)




